import { useState, useContext, useEffect, useCallback } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';
import StandardInput from './StandardInput';
import { SPACING } from './StandardStyles';

/**
 * Enhanced SearchBar Component
 *
 * A comprehensive search bar component with advanced functionality including:
 * - Debounced search
 * - Loading states
 * - Clear button
 * - Search button
 * - Controlled/Uncontrolled modes
 * - Focus/Blur handling
 * - Customizable styling
 *
 * @param {Object} props
 * @param {function} props.onSearch - Called when search text changes (debounced)
 * @param {string} props.placeholder - Input placeholder text
 * @param {string} props.value - Controlled value (optional)
 * @param {function} props.onChangeText - Controlled onChange handler (optional)
 * @param {boolean} props.showClearButton - Show clear button when text exists
 * @param {boolean} props.showSearchButton - Show dedicated search button
 * @param {boolean} props.isLoading - Show loading indicator
 * @param {number} props.debounceMs - Debounce delay in milliseconds (0 = no debounce)
 * @param {boolean} props.autoFocus - Auto focus on mount
 * @param {function} props.onSubmit - Called when search is submitted
 * @param {function} props.onFocus - Focus event handler
 * @param {function} props.onBlur - Blur event handler
 * @param {Object} props.containerStyle - Additional container styles
 * @param {boolean} props.disabled - Disable the search input
 * @param {string} props.keyboardType - Keyboard type
 * @param {string} props.returnKeyType - Return key type
 *
 * @example
 * // Basic usage
 * <SearchBar
 *   placeholder="Search users..."
 *   onSearch={(query) => console.log(query)}
 * />
 *
 * @example
 * // Advanced usage with all features
 * <SearchBar
 *   placeholder="Search chats..."
 *   onSearch={handleSearch}
 *   onSubmit={handleSearchSubmit}
 *   isLoading={isSearching}
 *   showClearButton={true}
 *   showSearchButton={false}
 *   debounceMs={300}
 *   autoFocus={false}
 * />
 *
 * @example
 * // Controlled mode
 * <SearchBar
 *   value={searchText}
 *   onChangeText={setSearchText}
 *   onSearch={performSearch}
 *   debounceMs={0} // No debounce in controlled mode
 * />
 */
export default function SearchBar({
    onSearch,
    placeholder = 'Search...',
    value,
    onChangeText,
    showClearButton = true,
    showSearchButton = false,
    isLoading = false,
    debounceMs = 300,
    autoFocus = false,
    onSubmit,
    onFocus,
    onBlur,
    containerStyle,
    disabled = false,
    keyboardType = 'default',
    returnKeyType = 'search',
}) {
    const { theme } = useContext(ThemeContext);
    const [internalSearchText, setInternalSearchText] = useState(value || '');
    const [isFocused, setIsFocused] = useState(false);

    // Use controlled or uncontrolled mode
    const searchText = value !== undefined ? value : internalSearchText;
    const setSearchText = onChangeText || setInternalSearchText;

    // Debounced search effect
    useEffect(() => {
        if (!onSearch || debounceMs <= 0) return;

        const timeoutId = setTimeout(() => {
            onSearch(searchText);
        }, debounceMs);

        return () => clearTimeout(timeoutId);
    }, [searchText, onSearch, debounceMs]);

    const handleSearchChange = useCallback(
        (text) => {
            setSearchText(text);

            // If no debounce, call onSearch immediately
            if (debounceMs <= 0 && onSearch) {
                onSearch(text);
            }
        },
        [setSearchText, onSearch, debounceMs]
    );

    const handleClear = useCallback(() => {
        setSearchText('');
        if (debounceMs <= 0 && onSearch) {
            onSearch('');
        }
    }, [setSearchText, onSearch, debounceMs]);

    const handleSubmit = useCallback(() => {
        if (onSubmit) {
            onSubmit(searchText);
        } else if (onSearch) {
            onSearch(searchText);
        }
    }, [onSubmit, onSearch, searchText]);

    const handleFocus = useCallback(
        (e) => {
            setIsFocused(true);
            onFocus?.(e);
        },
        [onFocus]
    );

    const handleBlur = useCallback(
        (e) => {
            setIsFocused(false);
            onBlur?.(e);
        },
        [onBlur]
    );

    return (
        <View style={[styles.container, containerStyle]}>
            <View
                style={[
                    styles.searchWrapper,
                    {
                        backgroundColor: theme.CARD,
                        borderColor: isFocused
                            ? theme.PRIMARY
                            : theme.INPUT_BORDER,
                        shadowColor: theme.PRIMARY,
                    },
                ]}
            >
                {/* Search Icon */}
                <Ionicons
                    name="search"
                    size={20}
                    color={isFocused ? theme.PRIMARY : theme.TEXT_SECONDARY}
                    style={styles.searchIcon}
                />

                {/* Search Input */}
                <StandardInput
                    placeholder={placeholder}
                    value={searchText}
                    onChangeText={handleSearchChange}
                    onSubmitEditing={handleSubmit}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    autoFocus={autoFocus}
                    keyboardType={keyboardType}
                    returnKeyType={returnKeyType}
                    disabled={disabled}
                    containerStyle={styles.inputContainer}
                    inputStyle={styles.inputText}
                />

                {/* Loading Indicator */}
                {isLoading && (
                    <ActivityIndicator
                        size="small"
                        color={theme.PRIMARY}
                        style={styles.loadingIndicator}
                    />
                )}

                {/* Clear Button */}
                {showClearButton && searchText.length > 0 && !isLoading && (
                    <TouchableOpacity
                        onPress={handleClear}
                        style={styles.clearButton}
                        disabled={disabled}
                    >
                        <Ionicons
                            name="close-circle"
                            size={20}
                            color={theme.TEXT_SECONDARY}
                        />
                    </TouchableOpacity>
                )}

                {/* Search Button */}
                {showSearchButton && (
                    <TouchableOpacity
                        onPress={handleSubmit}
                        style={[
                            styles.searchButton,
                            { backgroundColor: theme.PRIMARY },
                        ]}
                        disabled={disabled || isLoading}
                    >
                        <Ionicons name="search" size={18} color={theme.WHITE} />
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        paddingTop: SPACING.SM,
        paddingHorizontal: SPACING.MD,
    },
    searchWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 25,
        height: 50,
        paddingHorizontal: 16,
        borderWidth: 1,
        elevation: 2,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    searchIcon: {
        marginRight: 12,
    },
    inputContainer: {
        flex: 1,
        borderWidth: 0,
        backgroundColor: 'transparent',
        elevation: 0,
        shadowOpacity: 0,
        marginBottom: 0,
    },
    inputText: {
        fontSize: 16,
        paddingVertical: 0,
    },
    loadingIndicator: {
        marginLeft: 8,
    },
    clearButton: {
        marginLeft: 8,
        padding: 4,
    },
    searchButton: {
        marginLeft: 8,
        padding: 8,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
