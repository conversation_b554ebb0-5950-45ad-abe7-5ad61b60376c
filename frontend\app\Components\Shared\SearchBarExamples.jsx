import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import SearchBar from './SearchBar';

/**
 * SearchBar Usage Examples
 * 
 * This file demonstrates various ways to use the enhanced SearchBar component
 */
export default function SearchBarExamples() {
    const [searchResults, setSearchResults] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [controlledValue, setControlledValue] = useState('');

    // Example 1: Basic search with debouncing
    const handleBasicSearch = useCallback(async (query) => {
        console.log('Basic search:', query);
        if (!query.trim()) {
            setSearchResults([]);
            return;
        }
        
        setIsLoading(true);
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            setSearchResults([
                `Result 1 for "${query}"`,
                `Result 2 for "${query}"`,
                `Result 3 for "${query}"`
            ]);
        } catch (error) {
            console.error('Search failed:', error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Example 2: Immediate search (no debounce)
    const handleImmediateSearch = useCallback((query) => {
        console.log('Immediate search:', query);
    }, []);

    // Example 3: Search on submit only
    const handleSubmitSearch = useCallback((query) => {
        console.log('Submit search:', query);
        // Perform search only when user presses enter or search button
    }, []);

    // Example 4: Controlled search
    const handleControlledChange = useCallback((text) => {
        setControlledValue(text);
        console.log('Controlled value changed:', text);
    }, []);

    const handleControlledSearch = useCallback((query) => {
        console.log('Controlled search:', query);
    }, []);

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>SearchBar Examples</Text>

            {/* Example 1: Basic Debounced Search */}
            <View style={styles.example}>
                <Text style={styles.exampleTitle}>1. Basic Debounced Search</Text>
                <Text style={styles.description}>
                    Search with 300ms debounce, loading indicator, and clear button
                </Text>
                <SearchBar
                    placeholder="Search with debounce..."
                    onSearch={handleBasicSearch}
                    isLoading={isLoading}
                    showClearButton={true}
                    debounceMs={300}
                />
                {searchResults.length > 0 && (
                    <View style={styles.results}>
                        {searchResults.map((result, index) => (
                            <Text key={index} style={styles.result}>{result}</Text>
                        ))}
                    </View>
                )}
            </View>

            {/* Example 2: Immediate Search */}
            <View style={styles.example}>
                <Text style={styles.exampleTitle}>2. Immediate Search (No Debounce)</Text>
                <Text style={styles.description}>
                    Search triggers immediately on every character change
                </Text>
                <SearchBar
                    placeholder="Immediate search..."
                    onSearch={handleImmediateSearch}
                    debounceMs={0}
                    showClearButton={true}
                />
            </View>

            {/* Example 3: Search on Submit */}
            <View style={styles.example}>
                <Text style={styles.exampleTitle}>3. Search on Submit Only</Text>
                <Text style={styles.description}>
                    Search only when user presses enter or search button
                </Text>
                <SearchBar
                    placeholder="Press enter to search..."
                    onSubmit={handleSubmitSearch}
                    showSearchButton={true}
                    showClearButton={true}
                />
            </View>

            {/* Example 4: Controlled Mode */}
            <View style={styles.example}>
                <Text style={styles.exampleTitle}>4. Controlled Mode</Text>
                <Text style={styles.description}>
                    Parent component controls the search value
                </Text>
                <SearchBar
                    placeholder="Controlled search..."
                    value={controlledValue}
                    onChangeText={handleControlledChange}
                    onSearch={handleControlledSearch}
                    debounceMs={0}
                    showClearButton={true}
                />
                <Text style={styles.valueDisplay}>
                    Current value: "{controlledValue}"
                </Text>
            </View>

            {/* Example 5: Disabled State */}
            <View style={styles.example}>
                <Text style={styles.exampleTitle}>5. Disabled State</Text>
                <Text style={styles.description}>
                    Search bar in disabled state
                </Text>
                <SearchBar
                    placeholder="Disabled search..."
                    disabled={true}
                    showClearButton={true}
                />
            </View>

            {/* Example 6: Custom Styling */}
            <View style={styles.example}>
                <Text style={styles.exampleTitle}>6. Custom Styling</Text>
                <Text style={styles.description}>
                    Search bar with custom container styling
                </Text>
                <SearchBar
                    placeholder="Custom styled search..."
                    onSearch={handleBasicSearch}
                    containerStyle={styles.customContainer}
                    showClearButton={true}
                />
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: '#f5f5f5',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 20,
        textAlign: 'center',
    },
    example: {
        marginBottom: 30,
        backgroundColor: 'white',
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    exampleTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 8,
        color: '#333',
    },
    description: {
        fontSize: 14,
        color: '#666',
        marginBottom: 12,
        lineHeight: 20,
    },
    results: {
        marginTop: 12,
        padding: 12,
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
    },
    result: {
        fontSize: 14,
        color: '#333',
        marginBottom: 4,
    },
    valueDisplay: {
        marginTop: 8,
        fontSize: 14,
        color: '#666',
        fontStyle: 'italic',
    },
    customContainer: {
        backgroundColor: '#e3f2fd',
        borderRadius: 30,
    },
});
