import React, { useState, useContext, useCallback } from 'react';
import {
    View,
    Text,
    ScrollView,
    Image,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    Modal,
    Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useQuery, useMutation } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import {
    fetchLandById,
    expressInterest,
    scheduleSiteVisit,
    getLandVerificationStatus,
    getAIValidationResults,
} from '../../api/land/landApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';
import MapView, { Marker } from 'react-native-maps';

const { width } = Dimensions.get('window');

const LandDetails = () => {
    const { theme } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();
    const { landId } = useLocalSearchParams();

    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [showImageModal, setShowImageModal] = useState(false);
    const [showScheduleModal, setShowScheduleModal] = useState(false);

    // Fetch land details
    const {
        data: land,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['land', landId],
        queryFn: () => fetchLandById(landId),
        enabled: !!landId,
    });

    // Fetch verification status
    const { data: verificationStatus } = useQuery({
        queryKey: ['landVerification', landId],
        queryFn: () => getLandVerificationStatus(landId),
        enabled: !!landId,
    });

    // Fetch AI validation results
    const { data: aiValidation } = useQuery({
        queryKey: ['aiValidation', landId],
        queryFn: () => getAIValidationResults(landId),
        enabled: !!landId,
    });

    // Express interest mutation
    const expressInterestMutation = useMutation({
        mutationFn: (message) => expressInterest(landId, message),
        onSuccess: () => {
            showToast('success', 'Success', 'Interest expressed successfully');
            // Navigate to chat
            router.push({
                pathname: '/Chat/LandDiscussion',
                params: {
                    landId: land.id,
                    brokerId: land.broker?.id,
                    sellerId: land.seller?.id,
                },
            });
        },
        onError: () => {
            showToast('error', 'Error', 'Failed to express interest');
        },
    });

    // Schedule site visit mutation
    const scheduleVisitMutation = useMutation({
        mutationFn: (visitData) => scheduleSiteVisit(landId, visitData),
        onSuccess: () => {
            showToast(
                'success',
                'Success',
                'Site visit scheduled successfully'
            );
            setShowScheduleModal(false);
        },
        onError: () => {
            showToast('error', 'Error', 'Failed to schedule site visit');
        },
    });

    const handleExpressInterest = useCallback(() => {
        if (!user) {
            showToast(
                'error',
                'Authentication Required',
                'Please login to express interest'
            );
            return;
        }

        Alert.alert(
            'Express Interest',
            'Are you interested in this property?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Yes',
                    onPress: () =>
                        expressInterestMutation.mutate(
                            'Interested in this property'
                        ),
                },
            ]
        );
    }, [user, expressInterestMutation]);

    const handleScheduleVisit = useCallback(() => {
        if (!user) {
            showToast(
                'error',
                'Authentication Required',
                'Please login to schedule a visit'
            );
            return;
        }
        setShowScheduleModal(true);
    }, [user]);

    const handleContactBroker = useCallback(() => {
        if (!user) {
            showToast(
                'error',
                'Authentication Required',
                'Please login to contact broker'
            );
            return;
        }

        router.push({
            pathname: '/Chat/BrokerChat',
            params: {
                brokerId: land.broker?.id,
                landId: land.id,
            },
        });
    }, [user, land, router]);

    const renderImageCarousel = () => (
        <View style={styles.imageContainer}>
            <ScrollView
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onMomentumScrollEnd={(event) => {
                    const index = Math.round(
                        event.nativeEvent.contentOffset.x / width
                    );
                    setCurrentImageIndex(index);
                }}
            >
                {land?.images?.map((image, index) => (
                    <TouchableOpacity
                        key={index}
                        onPress={() => setShowImageModal(true)}
                        activeOpacity={0.9}
                    >
                        <Image
                            source={{ uri: image }}
                            style={styles.landImage}
                            resizeMode="cover"
                        />
                    </TouchableOpacity>
                ))}
            </ScrollView>

            {/* Image indicators */}
            <View style={styles.imageIndicators}>
                {land?.images?.map((_, index) => (
                    <View
                        key={index}
                        style={[
                            styles.indicator,
                            index === currentImageIndex &&
                                styles.activeIndicator,
                        ]}
                    />
                ))}
            </View>

            {/* Image counter */}
            <View style={styles.imageCounter}>
                <Text style={styles.imageCounterText}>
                    {currentImageIndex + 1} / {land?.images?.length || 0}
                </Text>
            </View>
        </View>
    );

    const renderVerificationBadge = () => {
        const status = verificationStatus?.status || land?.verificationStatus;
        const color =
            status === 'verified'
                ? '#4CAF50'
                : status === 'pending'
                  ? '#FF9800'
                  : '#F44336';

        return (
            <View
                style={[styles.verificationBadge, { backgroundColor: color }]}
            >
                <Ionicons
                    name={status === 'verified' ? 'checkmark-circle' : 'time'}
                    size={16}
                    color="#fff"
                />
                <Text style={styles.verificationText}>
                    {status === 'verified'
                        ? 'Verified'
                        : status === 'pending'
                          ? 'Verification Pending'
                          : 'Not Verified'}
                </Text>
            </View>
        );
    };

    const renderAIValidation = () => {
        if (!aiValidation) return null;

        return (
            <View
                style={[
                    styles.section,
                    { backgroundColor: theme.CARD_BACKGROUND },
                ]}
            >
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    AI Validation Results
                </Text>

                <View style={styles.validationItem}>
                    <Text
                        style={[
                            styles.validationLabel,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Document Authenticity
                    </Text>
                    <View
                        style={[
                            styles.validationScore,
                            {
                                backgroundColor:
                                    aiValidation.documentScore > 80
                                        ? '#4CAF50'
                                        : '#FF9800',
                            },
                        ]}
                    >
                        <Text style={styles.validationScoreText}>
                            {aiValidation.documentScore}%
                        </Text>
                    </View>
                </View>

                <View style={styles.validationItem}>
                    <Text
                        style={[
                            styles.validationLabel,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Price Analysis
                    </Text>
                    <Text
                        style={[
                            styles.validationValue,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        {aiValidation.priceAnalysis}
                    </Text>
                </View>

                {aiValidation.recommendations && (
                    <View style={styles.recommendationsContainer}>
                        <Text
                            style={[
                                styles.recommendationsTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            AI Recommendations
                        </Text>
                        {aiValidation.recommendations.map((rec, index) => (
                            <Text
                                key={index}
                                style={[
                                    styles.recommendation,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                • {rec}
                            </Text>
                        ))}
                    </View>
                )}
            </View>
        );
    };

    const renderMapView = () => {
        if (!land?.coordinates) return null;

        return (
            <View
                style={[
                    styles.section,
                    { backgroundColor: theme.CARD_BACKGROUND },
                ]}
            >
                <Text
                    style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    Location
                </Text>

                <View style={styles.mapContainer}>
                    <MapView
                        style={styles.map}
                        initialRegion={{
                            latitude: land.coordinates.latitude,
                            longitude: land.coordinates.longitude,
                            latitudeDelta: 0.01,
                            longitudeDelta: 0.01,
                        }}
                    >
                        <Marker
                            coordinate={{
                                latitude: land.coordinates.latitude,
                                longitude: land.coordinates.longitude,
                            }}
                            title={land.title}
                            description={land.location}
                        />
                    </MapView>
                </View>

                <View style={styles.locationInfo}>
                    <Ionicons name="location" size={20} color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.locationText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        {land.location}
                    </Text>
                </View>
            </View>
        );
    };

    if (isLoading) {
        return (
            <View
                style={[
                    styles.container,
                    styles.centered,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <Text
                    style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}
                >
                    Loading land details...
                </Text>
            </View>
        );
    }

    if (error || !land) {
        return (
            <View
                style={[
                    styles.container,
                    styles.centered,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <BackButton />
                <Text style={[styles.errorText, { color: theme.TEXT_PRIMARY }]}>
                    Failed to load land details
                </Text>
                <TouchableOpacity
                    style={[
                        styles.retryButton,
                        { backgroundColor: theme.PRIMARY },
                    ]}
                    onPress={refetch}
                >
                    <Text style={styles.retryText}>Retry</Text>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <ScrollView showsVerticalScrollIndicator={false}>
                {/* Header with back button */}
                <View style={styles.header}>
                    <BackButton color="#fff" />
                    <TouchableOpacity>
                        <Ionicons name="heart-outline" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>

                {/* Image carousel */}
                {renderImageCarousel()}

                {/* Land basic info */}
                <View
                    style={[
                        styles.section,
                        { backgroundColor: theme.CARD_BACKGROUND },
                    ]}
                >
                    <View style={styles.titleRow}>
                        <Text
                            style={[
                                styles.landTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {land.title}
                        </Text>
                        {renderVerificationBadge()}
                    </View>

                    <View style={styles.priceRow}>
                        <Text style={[styles.price, { color: theme.PRIMARY }]}>
                            ₹{land.price?.toLocaleString()}
                        </Text>
                        <Text
                            style={[
                                styles.pricePerUnit,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            ₹{Math.round(land.price / land.area)}/
                            {land.areaUnit}
                        </Text>
                    </View>

                    <View style={styles.detailsGrid}>
                        <View style={styles.detailItem}>
                            <Ionicons
                                name="resize-outline"
                                size={20}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.detailLabel,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Area
                            </Text>
                            <Text
                                style={[
                                    styles.detailValue,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {land.area} {land.areaUnit}
                            </Text>
                        </View>

                        <View style={styles.detailItem}>
                            <Ionicons
                                name="business-outline"
                                size={20}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.detailLabel,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Type
                            </Text>
                            <Text
                                style={[
                                    styles.detailValue,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {land.landType}
                            </Text>
                        </View>

                        <View style={styles.detailItem}>
                            <Ionicons
                                name="document-text-outline"
                                size={20}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.detailLabel,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Ownership
                            </Text>
                            <Text
                                style={[
                                    styles.detailValue,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {land.ownershipType}
                            </Text>
                        </View>
                    </View>
                </View>

                {/* Description */}
                <View
                    style={[
                        styles.section,
                        { backgroundColor: theme.CARD_BACKGROUND },
                    ]}
                >
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Description
                    </Text>
                    <Text
                        style={[
                            styles.description,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {land.description}
                    </Text>
                </View>

                {/* AI Validation */}
                {renderAIValidation()}

                {/* Map */}
                {renderMapView()}

                {/* Broker info */}
                {land.broker && (
                    <View
                        style={[
                            styles.section,
                            { backgroundColor: theme.CARD_BACKGROUND },
                        ]}
                    >
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Broker Information
                        </Text>

                        <View style={styles.brokerInfo}>
                            <Image
                                source={{
                                    uri:
                                        land.broker.avatar ||
                                        'https://via.placeholder.com/50',
                                }}
                                style={styles.brokerAvatar}
                            />
                            <View style={styles.brokerDetails}>
                                <Text
                                    style={[
                                        styles.brokerName,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {land.broker.name}
                                </Text>
                                <Text
                                    style={[
                                        styles.brokerRating,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    ⭐ {land.broker.rating} (
                                    {land.broker.reviewCount} reviews)
                                </Text>
                            </View>
                            <TouchableOpacity
                                style={[
                                    styles.contactButton,
                                    { backgroundColor: theme.PRIMARY },
                                ]}
                                onPress={handleContactBroker}
                            >
                                <Ionicons
                                    name="chatbubble"
                                    size={16}
                                    color="#fff"
                                />
                                <Text style={styles.contactText}>Contact</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                )}
            </ScrollView>

            {/* Bottom action buttons */}
            <View
                style={[
                    styles.bottomActions,
                    { backgroundColor: theme.CARD_BACKGROUND },
                ]}
            >
                <TouchableOpacity
                    style={[
                        styles.actionButton,
                        styles.scheduleButton,
                        { borderColor: theme.PRIMARY },
                    ]}
                    onPress={handleScheduleVisit}
                >
                    <Text
                        style={[
                            styles.scheduleButtonText,
                            { color: theme.PRIMARY },
                        ]}
                    >
                        Schedule Visit
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[
                        styles.actionButton,
                        styles.interestedButton,
                        { backgroundColor: theme.PRIMARY },
                    ]}
                    onPress={handleExpressInterest}
                    disabled={expressInterestMutation.isPending}
                >
                    <Text style={styles.interestedButtonText}>
                        {expressInterestMutation.isPending
                            ? 'Processing...'
                            : 'Express Interest'}
                    </Text>
                </TouchableOpacity>
            </View>

            {/* Image modal */}
            <Modal
                visible={showImageModal}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setShowImageModal(false)}
            >
                <View style={styles.imageModalOverlay}>
                    <TouchableOpacity
                        style={styles.closeImageModal}
                        onPress={() => setShowImageModal(false)}
                    >
                        <Ionicons name="close" size={30} color="#fff" />
                    </TouchableOpacity>

                    <ScrollView
                        horizontal
                        pagingEnabled
                        showsHorizontalScrollIndicator={false}
                        initialScrollIndex={currentImageIndex}
                    >
                        {land?.images?.map((image, index) => (
                            <Image
                                key={index}
                                source={{ uri: image }}
                                style={styles.fullScreenImage}
                                resizeMode="contain"
                            />
                        ))}
                    </ScrollView>
                </View>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    centered: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    header: {
        position: 'absolute',
        top: 50,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        zIndex: 1,
    },
    imageContainer: {
        position: 'relative',
    },
    landImage: {
        width: width,
        height: 300,
    },
    imageIndicators: {
        position: 'absolute',
        bottom: 20,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    indicator: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: 'rgba(255,255,255,0.5)',
        marginHorizontal: 4,
    },
    activeIndicator: {
        backgroundColor: '#fff',
    },
    imageCounter: {
        position: 'absolute',
        top: 20,
        right: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
    },
    imageCounterText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
    },
    section: {
        margin: 16,
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    titleRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    landTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        flex: 1,
        marginRight: 12,
    },
    verificationBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    verificationText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        marginLeft: 4,
    },
    priceRow: {
        flexDirection: 'row',
        alignItems: 'baseline',
        marginBottom: 16,
    },
    price: {
        fontSize: 28,
        fontWeight: 'bold',
        marginRight: 8,
    },
    pricePerUnit: {
        fontSize: 14,
    },
    detailsGrid: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    detailItem: {
        alignItems: 'center',
        flex: 1,
    },
    detailLabel: {
        fontSize: 12,
        marginTop: 4,
    },
    detailValue: {
        fontSize: 14,
        fontWeight: 'bold',
        marginTop: 2,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    description: {
        fontSize: 16,
        lineHeight: 24,
    },
    validationItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    validationLabel: {
        fontSize: 16,
    },
    validationScore: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
    },
    validationScoreText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    validationValue: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    recommendationsContainer: {
        marginTop: 16,
    },
    recommendationsTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    recommendation: {
        fontSize: 14,
        marginBottom: 4,
    },
    mapContainer: {
        height: 200,
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 12,
    },
    map: {
        flex: 1,
    },
    locationInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    locationText: {
        marginLeft: 8,
        fontSize: 16,
    },
    brokerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    brokerAvatar: {
        width: 50,
        height: 50,
        borderRadius: 25,
    },
    brokerDetails: {
        flex: 1,
        marginLeft: 12,
    },
    brokerName: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    brokerRating: {
        fontSize: 14,
        marginTop: 2,
    },
    contactButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
    },
    contactText: {
        color: '#fff',
        marginLeft: 4,
        fontWeight: 'bold',
    },
    bottomActions: {
        flexDirection: 'row',
        padding: 16,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    actionButton: {
        flex: 1,
        paddingVertical: 16,
        borderRadius: 12,
        alignItems: 'center',
        marginHorizontal: 8,
    },
    scheduleButton: {
        borderWidth: 2,
    },
    scheduleButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    interestedButton: {
        // backgroundColor set dynamically
    },
    interestedButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    imageModalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.9)',
        justifyContent: 'center',
    },
    closeImageModal: {
        position: 'absolute',
        top: 50,
        right: 20,
        zIndex: 1,
    },
    fullScreenImage: {
        width: width,
        height: '100%',
    },
    loadingText: {
        fontSize: 16,
    },
    errorText: {
        fontSize: 16,
        marginBottom: 16,
    },
    retryButton: {
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
    },
    retryText: {
        color: '#fff',
        fontWeight: 'bold',
    },
});

export default LandDetails;
