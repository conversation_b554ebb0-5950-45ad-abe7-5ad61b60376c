import React, { useState, useContext, useCallback } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import RazorpayCheckout from 'react-native-razorpay';
import { useStripe } from '@stripe/stripe-react-native';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import {
    createRazorpayOrder,
    verifyRazorpayPayment,
    createStripePaymentIntent,
    confirmStripePayment,
    getUserPaymentMethods,
    getWalletBalance,
} from '../../api/payment/paymentApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';

const PaymentScreen = () => {
    const { theme } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();
    const { initPaymentSheet, presentPaymentSheet } = useStripe();

    const {
        amount,
        type, // 'land_purchase', 'commission', 'service_payment', 'wallet_topup'
        itemId,
        description,
        recipientId,
    } = useLocalSearchParams();

    const [selectedPaymentMethod, setSelectedPaymentMethod] =
        useState('razorpay');
    const [useWallet, setUseWallet] = useState(false);
    const [processing, setProcessing] = useState(false);

    // Fetch user payment methods
    const { data: paymentMethods = [] } = useQuery({
        queryKey: ['paymentMethods', user?.id],
        queryFn: () => getUserPaymentMethods(user.id),
        enabled: !!user?.id,
    });

    // Fetch wallet balance
    const { data: walletBalance = 0 } = useQuery({
        queryKey: ['walletBalance', user?.id],
        queryFn: () => getWalletBalance(user.id),
        enabled: !!user?.id,
    });

    // Razorpay payment mutation
    const razorpayMutation = useMutation({
        mutationFn: createRazorpayOrder,
        onSuccess: (orderData) => {
            handleRazorpayPayment(orderData);
        },
        onError: (error) => {
            showToast('error', 'Error', 'Failed to create payment order');
            setProcessing(false);
        },
    });

    // Stripe payment mutation
    const stripeMutation = useMutation({
        mutationFn: createStripePaymentIntent,
        onSuccess: (paymentIntent) => {
            handleStripePayment(paymentIntent);
        },
        onError: (error) => {
            showToast('error', 'Error', 'Failed to create payment intent');
            setProcessing(false);
        },
    });

    const handleRazorpayPayment = useCallback(
        async (orderData) => {
            try {
                const options = {
                    description: description || 'Payment for BuildConnect',
                    image: 'https://your-logo-url.com/logo.png',
                    currency: 'INR',
                    key: orderData.key,
                    amount: orderData.amount,
                    order_id: orderData.id,
                    name: 'BuildConnect',
                    prefill: {
                        email: user.email,
                        contact: user.phone,
                        name: user.name,
                    },
                    theme: { color: theme.PRIMARY },
                };

                const data = await RazorpayCheckout.open(options);

                // Verify payment
                const verificationResult = await verifyRazorpayPayment({
                    razorpay_order_id: data.razorpay_order_id,
                    razorpay_payment_id: data.razorpay_payment_id,
                    razorpay_signature: data.razorpay_signature,
                    type,
                    itemId,
                    amount: parseFloat(amount),
                    recipientId,
                });

                if (verificationResult.success) {
                    showToast(
                        'success',
                        'Success',
                        'Payment completed successfully'
                    );
                    handlePaymentSuccess(verificationResult.transaction);
                } else {
                    showToast('error', 'Error', 'Payment verification failed');
                }
            } catch (error) {
                if (error.code === 'payment_cancelled') {
                    showToast('info', 'Cancelled', 'Payment was cancelled');
                } else {
                    showToast('error', 'Error', 'Payment failed');
                }
            } finally {
                setProcessing(false);
            }
        },
        [user, theme, description, type, itemId, amount, recipientId]
    );

    const handleStripePayment = useCallback(
        async (paymentIntent) => {
            try {
                const { error: initError } = await initPaymentSheet({
                    merchantDisplayName: 'BuildConnect',
                    paymentIntentClientSecret: paymentIntent.client_secret,
                    defaultBillingDetails: {
                        name: user.name,
                        email: user.email,
                    },
                });

                if (initError) {
                    showToast('error', 'Error', 'Failed to initialize payment');
                    setProcessing(false);
                    return;
                }

                const { error: presentError } = await presentPaymentSheet();

                if (presentError) {
                    if (presentError.code === 'Canceled') {
                        showToast('info', 'Cancelled', 'Payment was cancelled');
                    } else {
                        showToast('error', 'Error', 'Payment failed');
                    }
                } else {
                    // Confirm payment on backend
                    const confirmResult = await confirmStripePayment(
                        paymentIntent.id,
                        paymentIntent.payment_method
                    );

                    if (confirmResult.success) {
                        showToast(
                            'success',
                            'Success',
                            'Payment completed successfully'
                        );
                        handlePaymentSuccess(confirmResult.transaction);
                    } else {
                        showToast(
                            'error',
                            'Error',
                            'Payment confirmation failed'
                        );
                    }
                }
            } catch (error) {
                showToast('error', 'Error', 'Payment failed');
            } finally {
                setProcessing(false);
            }
        },
        [user, initPaymentSheet, presentPaymentSheet]
    );

    const handlePaymentSuccess = useCallback(
        (transaction) => {
            // Navigate based on payment type
            switch (type) {
                case 'land_purchase':
                    router.replace({
                        pathname: '/Properties/PurchaseSuccess',
                        params: {
                            transactionId: transaction.id,
                            landId: itemId,
                        },
                    });
                    break;
                case 'service_payment':
                    router.replace({
                        pathname: '/Contractors/PaymentSuccess',
                        params: {
                            transactionId: transaction.id,
                            serviceId: itemId,
                        },
                    });
                    break;
                case 'wallet_topup':
                    router.replace({
                        pathname: '/Profile/WalletSuccess',
                        params: { transactionId: transaction.id },
                    });
                    break;
                default:
                    router.replace({
                        pathname: '/Payment/PaymentSuccess',
                        params: { transactionId: transaction.id },
                    });
            }
        },
        [type, itemId, router]
    );

    const handlePayment = useCallback(async () => {
        if (processing) return;

        setProcessing(true);

        const paymentData = {
            amount: parseFloat(amount),
            type,
            itemId,
            recipientId,
            useWallet,
            walletAmount: useWallet
                ? Math.min(walletBalance, parseFloat(amount))
                : 0,
        };

        if (selectedPaymentMethod === 'razorpay') {
            razorpayMutation.mutate(paymentData);
        } else if (selectedPaymentMethod === 'stripe') {
            stripeMutation.mutate(paymentData);
        }
    }, [
        processing,
        amount,
        type,
        itemId,
        recipientId,
        useWallet,
        walletBalance,
        selectedPaymentMethod,
        razorpayMutation,
        stripeMutation,
    ]);

    const formatAmount = useCallback((amt) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
        }).format(amt);
    }, []);

    const getPaymentTypeTitle = useCallback(() => {
        switch (type) {
            case 'land_purchase':
                return 'Land Purchase Payment';
            case 'commission':
                return 'Commission Payment';
            case 'service_payment':
                return 'Service Payment';
            case 'wallet_topup':
                return 'Add Money to Wallet';
            default:
                return 'Payment';
        }
    }, [type]);

    const calculateFinalAmount = useCallback(() => {
        const baseAmount = parseFloat(amount);
        const walletDeduction = useWallet
            ? Math.min(walletBalance, baseAmount)
            : 0;
        return baseAmount - walletDeduction;
    }, [amount, useWallet, walletBalance]);

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <BackButton color="#fff" />
                <Text style={styles.headerTitle}>{getPaymentTypeTitle()}</Text>
                <View style={{ width: 24 }} />
            </LinearGradient>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
            >
                {/* Payment Summary */}
                <View
                    style={[
                        styles.section,
                        { backgroundColor: theme.CARD_BACKGROUND },
                    ]}
                >
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Payment Summary
                    </Text>

                    <View style={styles.summaryRow}>
                        <Text
                            style={[
                                styles.summaryLabel,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Amount
                        </Text>
                        <Text
                            style={[
                                styles.summaryValue,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            {formatAmount(amount)}
                        </Text>
                    </View>

                    {description && (
                        <View style={styles.summaryRow}>
                            <Text
                                style={[
                                    styles.summaryLabel,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Description
                            </Text>
                            <Text
                                style={[
                                    styles.summaryValue,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {description}
                            </Text>
                        </View>
                    )}

                    {useWallet && walletBalance > 0 && (
                        <View style={styles.summaryRow}>
                            <Text
                                style={[
                                    styles.summaryLabel,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Wallet Deduction
                            </Text>
                            <Text
                                style={[
                                    styles.summaryValue,
                                    { color: '#4CAF50' },
                                ]}
                            >
                                -
                                {formatAmount(
                                    Math.min(walletBalance, parseFloat(amount))
                                )}
                            </Text>
                        </View>
                    )}

                    <View style={[styles.summaryRow, styles.totalRow]}>
                        <Text
                            style={[
                                styles.totalLabel,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Total to Pay
                        </Text>
                        <Text
                            style={[
                                styles.totalValue,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            {formatAmount(calculateFinalAmount())}
                        </Text>
                    </View>
                </View>

                {/* Wallet Option */}
                {walletBalance > 0 && (
                    <View
                        style={[
                            styles.section,
                            { backgroundColor: theme.CARD_BACKGROUND },
                        ]}
                    >
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Wallet Balance
                        </Text>

                        <View style={styles.walletRow}>
                            <View style={styles.walletInfo}>
                                <Text
                                    style={[
                                        styles.walletBalance,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    {formatAmount(walletBalance)}
                                </Text>
                                <Text
                                    style={[
                                        styles.walletLabel,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Available Balance
                                </Text>
                            </View>

                            <TouchableOpacity
                                style={[
                                    styles.walletToggle,
                                    {
                                        backgroundColor: useWallet
                                            ? theme.PRIMARY
                                            : theme.INPUT_BACKGROUND,
                                    },
                                ]}
                                onPress={() => setUseWallet(!useWallet)}
                            >
                                <Ionicons
                                    name={useWallet ? 'checkmark' : 'close'}
                                    size={20}
                                    color={
                                        useWallet
                                            ? '#fff'
                                            : theme.TEXT_SECONDARY
                                    }
                                />
                            </TouchableOpacity>
                        </View>

                        <Text
                            style={[
                                styles.walletHint,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {useWallet
                                ? `₹${Math.min(walletBalance, parseFloat(amount))} will be deducted from your wallet`
                                : 'Toggle to use wallet balance'}
                        </Text>
                    </View>
                )}

                {/* Payment Methods */}
                {calculateFinalAmount() > 0 && (
                    <View
                        style={[
                            styles.section,
                            { backgroundColor: theme.CARD_BACKGROUND },
                        ]}
                    >
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Payment Method
                        </Text>

                        <TouchableOpacity
                            style={[
                                styles.paymentMethod,
                                selectedPaymentMethod === 'razorpay' &&
                                    styles.selectedMethod,
                                {
                                    borderColor:
                                        selectedPaymentMethod === 'razorpay'
                                            ? theme.PRIMARY
                                            : theme.BORDER,
                                },
                            ]}
                            onPress={() => setSelectedPaymentMethod('razorpay')}
                        >
                            <View style={styles.methodInfo}>
                                <Ionicons
                                    name="card"
                                    size={24}
                                    color={theme.PRIMARY}
                                />
                                <View style={styles.methodText}>
                                    <Text
                                        style={[
                                            styles.methodName,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Razorpay
                                    </Text>
                                    <Text
                                        style={[
                                            styles.methodDesc,
                                            { color: theme.TEXT_SECONDARY },
                                        ]}
                                    >
                                        UPI, Cards, Net Banking, Wallets
                                    </Text>
                                </View>
                            </View>
                            <View
                                style={[
                                    styles.radioButton,
                                    { borderColor: theme.PRIMARY },
                                    selectedPaymentMethod === 'razorpay' && {
                                        backgroundColor: theme.PRIMARY,
                                    },
                                ]}
                            >
                                {selectedPaymentMethod === 'razorpay' && (
                                    <Ionicons
                                        name="checkmark"
                                        size={16}
                                        color="#fff"
                                    />
                                )}
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={[
                                styles.paymentMethod,
                                selectedPaymentMethod === 'stripe' &&
                                    styles.selectedMethod,
                                {
                                    borderColor:
                                        selectedPaymentMethod === 'stripe'
                                            ? theme.PRIMARY
                                            : theme.BORDER,
                                },
                            ]}
                            onPress={() => setSelectedPaymentMethod('stripe')}
                        >
                            <View style={styles.methodInfo}>
                                <Ionicons
                                    name="card-outline"
                                    size={24}
                                    color={theme.PRIMARY}
                                />
                                <View style={styles.methodText}>
                                    <Text
                                        style={[
                                            styles.methodName,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Stripe
                                    </Text>
                                    <Text
                                        style={[
                                            styles.methodDesc,
                                            { color: theme.TEXT_SECONDARY },
                                        ]}
                                    >
                                        International Cards, Apple Pay, Google
                                        Pay
                                    </Text>
                                </View>
                            </View>
                            <View
                                style={[
                                    styles.radioButton,
                                    { borderColor: theme.PRIMARY },
                                    selectedPaymentMethod === 'stripe' && {
                                        backgroundColor: theme.PRIMARY,
                                    },
                                ]}
                            >
                                {selectedPaymentMethod === 'stripe' && (
                                    <Ionicons
                                        name="checkmark"
                                        size={16}
                                        color="#fff"
                                    />
                                )}
                            </View>
                        </TouchableOpacity>
                    </View>
                )}

                {/* Security Info */}
                <View
                    style={[
                        styles.section,
                        { backgroundColor: theme.CARD_BACKGROUND },
                    ]}
                >
                    <View style={styles.securityHeader}>
                        <Ionicons
                            name="shield-checkmark"
                            size={24}
                            color="#4CAF50"
                        />
                        <Text
                            style={[
                                styles.securityTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Secure Payment
                        </Text>
                    </View>
                    <Text
                        style={[
                            styles.securityText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        Your payment information is encrypted and secure. We
                        don't store your card details.
                    </Text>
                </View>
            </ScrollView>

            {/* Pay Button */}
            <View
                style={[
                    styles.footer,
                    { backgroundColor: theme.CARD_BACKGROUND },
                ]}
            >
                <TouchableOpacity
                    style={[
                        styles.payButton,
                        { backgroundColor: theme.PRIMARY },
                        (processing || calculateFinalAmount() <= 0) &&
                            styles.payButtonDisabled,
                    ]}
                    onPress={handlePayment}
                    disabled={processing || calculateFinalAmount() <= 0}
                >
                    {processing ? (
                        <ActivityIndicator color="#fff" size="small" />
                    ) : (
                        <Text style={styles.payButtonText}>
                            {calculateFinalAmount() <= 0
                                ? 'Complete Payment'
                                : `Pay ${formatAmount(calculateFinalAmount())}`}
                        </Text>
                    )}
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingTop: 50,
        paddingBottom: 20,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    content: {
        flex: 1,
    },
    section: {
        margin: 16,
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    summaryLabel: {
        fontSize: 16,
    },
    summaryValue: {
        fontSize: 16,
        fontWeight: '600',
    },
    totalRow: {
        borderTopWidth: 1,
        borderTopColor: '#eee',
        paddingTop: 12,
        marginTop: 8,
    },
    totalLabel: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    totalValue: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    walletRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    walletInfo: {
        flex: 1,
    },
    walletBalance: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    walletLabel: {
        fontSize: 14,
        marginTop: 2,
    },
    walletToggle: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    walletHint: {
        fontSize: 12,
        fontStyle: 'italic',
    },
    paymentMethod: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 16,
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 12,
    },
    selectedMethod: {
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    methodInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    methodText: {
        marginLeft: 12,
        flex: 1,
    },
    methodName: {
        fontSize: 16,
        fontWeight: '600',
    },
    methodDesc: {
        fontSize: 12,
        marginTop: 2,
    },
    radioButton: {
        width: 24,
        height: 24,
        borderRadius: 12,
        borderWidth: 2,
        alignItems: 'center',
        justifyContent: 'center',
    },
    securityHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    securityTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
    },
    securityText: {
        fontSize: 14,
        lineHeight: 20,
    },
    footer: {
        padding: 16,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    payButton: {
        paddingVertical: 16,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    payButtonDisabled: {
        opacity: 0.6,
    },
    payButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default PaymentScreen;
