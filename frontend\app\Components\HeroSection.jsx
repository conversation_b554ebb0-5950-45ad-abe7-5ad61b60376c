// components/HeroSection.js
import React, { useState, useContext } from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemeContext } from '../../context/ThemeContext';
import SearchBar from './Shared/SearchBar';

export default function HeroSection({ onFindContractors }) {
    const { theme } = useContext(ThemeContext);

    const handleSearch = (query) => {
        if (query.trim() && onFindContractors) {
            onFindContractors(query);
        }
    };

    const handleSearchSubmit = (query) => {
        if (query.trim() && onFindContractors) {
            onFindContractors(query);
        }
    };

    return (
        <View style={[styles.hero, { backgroundColor: theme.WHITE }]}>
            {/* Search Bar */}
            <SearchBar
                placeholder="Find a Contractor or Land Near You"
                onSearch={handleSearch}
                onSubmit={handleSearchSubmit}
                showClearButton={true}
                showSearchButton={true}
                debounceMs={0}
                containerStyle={styles.searchWrapper}
            />

            {/* Action Buttons */}
            <View style={styles.buttons}>
                {/* <TouchableOpacity
          style={[styles.button, styles.contractorsBtn,backgroundColor: theme.PRIMARY,]}
          onPress={() => onFindContractors && onFindContractors(query)}
        >
          <Text style={styles.buttonText}>Find Contractors</Text>
        </TouchableOpacity> */}

                {/* <TouchableOpacity
          style={[styles.button, styles.landBtn,backgroundColor: theme.PRIMARY,]}
          onPress={() => onFindLand && onFindLand(query)}
        >
          <Text style={styles.buttonText}>Find Land</Text>
        </TouchableOpacity> */}
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    hero: {
        padding: 16,
    },
    searchWrapper: {
        position: 'relative',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f1f1f1',
        borderRadius: 20,
        paddingHorizontal: 12,
        marginBottom: 16,
    },
    searchIcon: {
        marginRight: 8,
    },
    searchInput: {
        flex: 1,
        height: 40,
        fontSize: 16,
        color: '#333',
    },
    buttons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    button: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
    },
    contractorsBtn: {
        marginRight: 8,
    },
    landBtn: {
        marginLeft: 8,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});
