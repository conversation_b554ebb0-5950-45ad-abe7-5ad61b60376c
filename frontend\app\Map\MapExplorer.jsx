import React, {
    useState,
    useContext,
    useEffect,
    useCallback,
    useRef,
} from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Modal,
    ScrollView,
    SafeAreaView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import MapView, { Marker, Callout, Circle } from 'react-native-maps';
import { useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { AuthContext } from '../../context/AuthContext';
import {
    getCurrentLocation,
    getNearbyLands,
    getNearbyPOIs,
    calculateDistance,
    filterBrokersByServiceArea,
    filterContractorsByServiceArea,
    geocodeAddress,
} from '../../api/map/mapApi';
import { fetchAllBrokers } from '../../api/broker/brokerApi';
import { fetchAllContractors } from '../../api/contractor/contractorApi';
import SearchBar from '../Components/Shared/SearchBar';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';

const MapExplorer = () => {
    const { theme } = useContext(ThemeContext);
    const { user } = useContext(AuthContext);
    const router = useRouter();
    const mapRef = useRef(null);

    const [currentLocation, setCurrentLocation] = useState(null);
    const [mapRegion, setMapRegion] = useState({
        latitude: 28.6139, // Default to Delhi
        longitude: 77.209,
        latitudeDelta: 0.1,
        longitudeDelta: 0.1,
    });
    const [locationName, setLocationName] = useState('Delhi');
    const [selectedMarker, setSelectedMarker] = useState(null);
    const [showFilters, setShowFilters] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [activeLayer, setActiveLayer] = useState('lands'); // 'lands', 'brokers', 'contractors', 'poi'
    const [radius, setRadius] = useState(10); // km
    const [showRadius, setShowRadius] = useState(false);

    // Search functionality
    const handleSearch = useCallback(async (query) => {
        if (!query.trim()) {
            setSearchResults([]);
            return;
        }

        setIsSearching(true);
        try {
            const results = await geocodeAddress(query);
            if (results && results.length > 0) {
                setSearchResults(results);
                // Move map to first search result
                const firstResult = results[0];
                mapRef.current?.animateToRegion({
                    latitude: firstResult.latitude,
                    longitude: firstResult.longitude,
                    latitudeDelta: 0.05,
                    longitudeDelta: 0.05,
                });
            }
        } catch (error) {
            console.warn('Search failed:', error);
            showToast('error', 'Search Error', 'Failed to search location');
        } finally {
            setIsSearching(false);
        }
    }, []);

    // Get current location on mount
    useEffect(() => {
        getCurrentLocation()
            .then((location) => {
                setCurrentLocation(location);
                setMapRegion({
                    latitude: location.latitude,
                    longitude: location.longitude,
                    latitudeDelta: 0.05,
                    longitudeDelta: 0.05,
                });
            })
            .catch((error) => {
                showToast(
                    'error',
                    'Location Error',
                    'Failed to get your location'
                );
            });
    }, []);

    // Fetch nearby lands
    const { data: nearbyLands = [], refetch: refetchLands } = useQuery({
        queryKey: [
            'nearbyLands',
            mapRegion.latitude,
            mapRegion.longitude,
            radius,
        ],
        queryFn: () =>
            getNearbyLands(mapRegion.latitude, mapRegion.longitude, radius),
        enabled: activeLayer === 'lands',
    });

    // Fetch all brokers and filter by service area
    const { data: allBrokers = [], refetch: refetchBrokers } = useQuery({
        queryKey: ['allBrokers'],
        queryFn: fetchAllBrokers,
        enabled: activeLayer === 'brokers',
    });

    // Filter brokers by service area and distance
    const nearbyBrokers = React.useMemo(() => {
        if (!allBrokers.length || !mapRegion) return [];

        return filterBrokersByServiceArea(
            allBrokers,
            mapRegion.latitude,
            mapRegion.longitude,
            radius,
            locationName
        );
    }, [
        allBrokers,
        mapRegion.latitude,
        mapRegion.longitude,
        locationName,
        radius,
    ]);

    // Fetch all contractors and filter by service area
    const { data: allContractors = [], refetch: refetchContractors } = useQuery(
        {
            queryKey: ['allContractors'],
            queryFn: fetchAllContractors,
            enabled: activeLayer === 'contractors',
        }
    );

    // Filter contractors by service area and distance
    const nearbyContractors = React.useMemo(() => {
        if (!allContractors.length || !mapRegion) return [];

        return filterContractorsByServiceArea(
            allContractors,
            mapRegion.latitude,
            mapRegion.longitude,
            radius,
            locationName
        );
    }, [
        allContractors,
        mapRegion.latitude,
        mapRegion.longitude,
        locationName,
        radius,
    ]);

    // Fetch nearby POIs
    const { data: nearbyPOIs = [], refetch: refetchPOIs } = useQuery({
        queryKey: [
            'nearbyPOIs',
            mapRegion.latitude,
            mapRegion.longitude,
            radius,
        ],
        queryFn: () =>
            getNearbyPOIs(
                mapRegion.latitude,
                mapRegion.longitude,
                'all',
                radius
            ),
        enabled: activeLayer === 'poi',
    });

    const handleRegionChange = useCallback((region) => {
        // Simple region update without async operations to prevent infinite loops
        setMapRegion((prev) => {
            // Only update if the region has changed significantly (more than 0.001 degrees)
            const latDiff = Math.abs(prev.latitude - region.latitude);
            const lngDiff = Math.abs(prev.longitude - region.longitude);

            if (latDiff < 0.001 && lngDiff < 0.001) {
                return prev; // Return same object to prevent re-renders
            }

            return region;
        });
    }, []);

    const handleMarkerPress = useCallback((marker) => {
        setSelectedMarker(marker);
    }, []);

    const handleMyLocationPress = useCallback(() => {
        if (currentLocation) {
            mapRef.current?.animateToRegion({
                latitude: currentLocation.latitude,
                longitude: currentLocation.longitude,
                latitudeDelta: 0.05,
                longitudeDelta: 0.05,
            });
        } else {
            getCurrentLocation()
                .then((location) => {
                    setCurrentLocation(location);
                    mapRef.current?.animateToRegion({
                        latitude: location.latitude,
                        longitude: location.longitude,
                        latitudeDelta: 0.05,
                        longitudeDelta: 0.05,
                    });
                })
                .catch((error) => {
                    showToast('error', 'Error', 'Failed to get your location');
                });
        }
    }, [currentLocation]);

    const handleLayerChange = useCallback((layer) => {
        setActiveLayer(layer);
        setSelectedMarker(null);
    }, []);

    const handleMarkerCalloutPress = useCallback(
        (marker) => {
            switch (activeLayer) {
                case 'lands':
                    router.push({
                        pathname: '/Properties/LandDetails',
                        params: { landId: marker.id },
                    });
                    break;
                case 'brokers':
                    router.push({
                        pathname: '/Brokers/BrokerProfile',
                        params: { brokerId: marker.id },
                    });
                    break;
                case 'contractors':
                    router.push({
                        pathname: '/Contractors/ContractorProfile',
                        params: { contractorId: marker.id },
                    });
                    break;
                default:
                    break;
            }
        },
        [activeLayer, router]
    );

    const handleSearchChange = useCallback(
        (query) => {
            setSearchQuery(query);
            if (query.trim()) {
                handleSearch(query);
            } else {
                setSearchResults([]);
            }
        },
        [handleSearch]
    );

    const handleSearchSubmit = useCallback(
        (query) => {
            if (query.trim()) {
                handleSearch(query);
            }
        },
        [handleSearch]
    );

    const getMarkerColor = useCallback(
        (type) => {
            switch (type) {
                case 'lands':
                    return theme.PRIMARY;
                case 'brokers':
                    return '#4CAF50';
                case 'contractors':
                    return '#FF9800';
                case 'poi':
                    return '#9C27B0';
                default:
                    return theme.PRIMARY;
            }
        },
        [theme]
    );

    const getMarkerIcon = useCallback((type, category) => {
        switch (type) {
            case 'lands':
                return 'location';
            case 'brokers':
                return 'person';
            case 'contractors':
                return 'hammer';
            case 'poi':
                switch (category) {
                    case 'school':
                        return 'school';
                    case 'hospital':
                        return 'medical';
                    case 'bank':
                        return 'card';
                    case 'transport':
                        return 'train';
                    default:
                        return 'business';
                }
            default:
                return 'location';
        }
    }, []);

    const formatDistance = useCallback((distance) => {
        if (distance < 1) {
            return `${Math.round(distance * 1000)}m`;
        }
        return `${distance.toFixed(1)}km`;
    }, []);

    const formatPrice = useCallback((price) => {
        if (price >= ********) {
            return `₹${(price / ********).toFixed(1)}Cr`;
        } else if (price >= 100000) {
            return `₹${(price / 100000).toFixed(1)}L`;
        }
        return `₹${price.toLocaleString()}`;
    }, []);

    // State for processed markers
    const [processedMarkers, setProcessedMarkers] = useState([]);

    // Process markers when data changes
    useEffect(() => {
        const processMarkers = async () => {
            let markers = [];

            switch (activeLayer) {
                case 'lands': {
                    markers = nearbyLands.map((land) => ({
                        ...land,
                        type: 'lands',
                        coordinate: {
                            latitude: land.coordinates.latitude,
                            longitude: land.coordinates.longitude,
                        },
                    }));
                    break;
                }
                case 'brokers': {
                    // Process brokers with simple fallback coordinates
                    markers = nearbyBrokers.map((broker) => {
                        let coordinates = broker.coordinates || broker.location;

                        // Simple fallback without async geocoding
                        if (!coordinates) {
                            // Use service area name as a seed for consistent random coordinates
                            const seed =
                                broker.serviceAreas &&
                                broker.serviceAreas.length > 0
                                    ? broker.serviceAreas[0].charCodeAt(0) / 255
                                    : Math.random();

                            coordinates = {
                                latitude:
                                    mapRegion.latitude + (seed - 0.5) * 0.05,
                                longitude:
                                    mapRegion.longitude + (seed - 0.5) * 0.05,
                            };
                        }

                        return {
                            ...broker,
                            type: 'brokers',
                            coordinate: coordinates,
                            name:
                                broker.name ||
                                broker.nameOnAadhaar ||
                                'Unknown Broker',
                            specialization:
                                broker.specialization || 'Real Estate Broker',
                        };
                    });
                    break;
                }
                case 'contractors': {
                    // Process contractors with simple fallback coordinates
                    markers = nearbyContractors.map((contractor) => {
                        let coordinates =
                            contractor.coordinates || contractor.location;

                        // Simple fallback without async geocoding
                        if (!coordinates) {
                            // Use service area name as a seed for consistent random coordinates
                            const seed =
                                contractor.serviceAreas &&
                                contractor.serviceAreas.length > 0
                                    ? contractor.serviceAreas[0].charCodeAt(0) /
                                      255
                                    : Math.random();

                            coordinates = {
                                latitude:
                                    mapRegion.latitude + (seed - 0.5) * 0.05,
                                longitude:
                                    mapRegion.longitude + (seed - 0.5) * 0.05,
                            };
                        }

                        return {
                            ...contractor,
                            type: 'contractors',
                            coordinate: coordinates,
                            name:
                                contractor.name ||
                                contractor.nameOnAadhaar ||
                                'Unknown Contractor',
                            services: contractor.specialties ||
                                contractor.services || ['Construction'],
                        };
                    });
                    break;
                }
                case 'poi': {
                    markers = nearbyPOIs.map((poi) => ({
                        ...poi,
                        type: 'poi',
                        coordinate: {
                            latitude: poi.coordinates.latitude,
                            longitude: poi.coordinates.longitude,
                        },
                    }));
                    break;
                }
            }

            setProcessedMarkers(markers);
        };

        processMarkers();
    }, [
        activeLayer,
        nearbyLands,
        nearbyBrokers,
        nearbyContractors,
        nearbyPOIs,
        mapRegion.latitude,
        mapRegion.longitude,
    ]);

    // Render markers from processed state
    const renderMarkers = useCallback(() => {
        return processedMarkers.map((marker, index) => {
            const distance = currentLocation
                ? calculateDistance(
                      currentLocation.latitude,
                      currentLocation.longitude,
                      marker.coordinate.latitude,
                      marker.coordinate.longitude
                  )
                : 0;

            return (
                <Marker
                    key={`${marker.type}-${marker.id}-${index}`}
                    coordinate={marker.coordinate}
                    pinColor={getMarkerColor(marker.type)}
                    onPress={() => handleMarkerPress(marker)}
                >
                    <Callout onPress={() => handleMarkerCalloutPress(marker)}>
                        <View style={styles.calloutContainer}>
                            <Text style={styles.calloutTitle} numberOfLines={1}>
                                {marker.title || marker.name}
                            </Text>
                            {marker.type === 'lands' && (
                                <Text style={styles.calloutPrice}>
                                    {formatPrice(marker.price)}
                                </Text>
                            )}
                            {marker.type === 'brokers' && (
                                <Text style={styles.calloutSubtitle}>
                                    {marker.specialization}
                                </Text>
                            )}
                            {marker.type === 'contractors' && (
                                <Text style={styles.calloutSubtitle}>
                                    {marker.services?.join(', ')}
                                </Text>
                            )}
                            <Text style={styles.calloutDistance}>
                                {formatDistance(distance)} away
                            </Text>
                        </View>
                    </Callout>
                </Marker>
            );
        });
    }, [
        processedMarkers,
        currentLocation,
        getMarkerColor,
        handleMarkerPress,
        handleMarkerCalloutPress,
        formatDistance,
        formatPrice,
    ]);

    const renderLayerButtons = () => (
        <SafeAreaView
            style={[
                styles.layerContainer,
                { backgroundColor: theme.BACKGROUND },
            ]}
        >
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.layerContent}
            >
                {[
                    { key: 'lands', label: 'Lands', icon: 'location' },
                    { key: 'brokers', label: 'Brokers', icon: 'person' },
                    {
                        key: 'contractors',
                        label: 'Contractors',
                        icon: 'hammer',
                    },
                    { key: 'poi', label: 'POI', icon: 'business' },
                ].map((layer) => (
                    <TouchableOpacity
                        key={layer.key}
                        style={[
                            styles.layerButton,
                            activeLayer === layer.key && {
                                backgroundColor:
                                    activeLayer === layer.key
                                        ? theme.PRIMARY
                                        : 'transparent',
                                borderColor: theme.PRIMARY,
                            },
                        ]}
                        onPress={() => handleLayerChange(layer.key)}
                    >
                        <Ionicons
                            name={layer.icon}
                            size={16}
                            color={
                                activeLayer === layer.key
                                    ? theme.WHITE
                                    : theme.PRIMARY
                            }
                        />
                        <Text
                            style={[
                                styles.layerButtonText,
                                {
                                    color:
                                        activeLayer === layer.key
                                            ? theme.WHITE
                                            : theme.PRIMARY,
                                },
                            ]}
                        >
                            {layer.label}
                        </Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </SafeAreaView>
    );

    const renderFilterModal = () => (
        <Modal
            visible={showFilters}
            animationType="slide"
            transparent
            onRequestClose={() => setShowFilters(false)}
        >
            <View style={styles.modalOverlay}>
                <View
                    style={[
                        styles.modalContent,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <View style={styles.modalHeader}>
                        <Text
                            style={[
                                styles.modalTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Map Filters
                        </Text>
                        <TouchableOpacity onPress={() => setShowFilters(false)}>
                            <Ionicons
                                name="close"
                                size={24}
                                color={theme.TEXT_SECONDARY}
                            />
                        </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.modalBody}>
                        <View style={styles.filterSection}>
                            <Text
                                style={[
                                    styles.filterLabel,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Search Radius: {radius}km
                            </Text>
                            <View style={styles.radiusContainer}>
                                {[5, 10, 20, 50].map((r) => (
                                    <TouchableOpacity
                                        key={r}
                                        style={[
                                            styles.radiusButton,
                                            radius === r && {
                                                backgroundColor: theme.PRIMARY,
                                            },
                                            { borderColor: theme.PRIMARY },
                                        ]}
                                        onPress={() => setRadius(r)}
                                    >
                                        <Text
                                            style={[
                                                styles.radiusButtonText,
                                                {
                                                    color:
                                                        radius === r
                                                            ? theme.TEXT_PRIMARY
                                                            : theme.PRIMARY,
                                                    borderColor: theme.PRIMARY,
                                                },
                                            ]}
                                        >
                                            {r}km
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </View>

                        <View style={styles.filterSection}>
                            <Text
                                style={[
                                    styles.filterLabel,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Show Search Radius
                            </Text>
                            <TouchableOpacity
                                style={[
                                    styles.toggleButton,
                                    {
                                        backgroundColor: showRadius
                                            ? theme.PRIMARY
                                            : theme.BACKGROUND,
                                    },
                                ]}
                                onPress={() => setShowRadius(!showRadius)}
                            >
                                <Ionicons
                                    name={showRadius ? 'checkmark' : 'close'}
                                    size={20}
                                    color={
                                        showRadius
                                            ? '#fff'
                                            : theme.TEXT_SECONDARY
                                    }
                                />
                            </TouchableOpacity>
                        </View>
                    </ScrollView>
                </View>
            </View>
        </Modal>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <BackButton color="#fff" />
                <Text style={[styles.headerTitle, { color: theme.WHITE }]}>
                    Map Explorer
                </Text>
                <TouchableOpacity onPress={() => setShowFilters(true)}>
                    <Ionicons name="options" size={24} color="#fff" />
                </TouchableOpacity>
            </LinearGradient>

            {/* Search Bar */}
            <SearchBar
                placeholder="Search location..."
                onSearch={handleSearchChange}
                onSubmit={handleSearchSubmit}
                isLoading={isSearching}
                showClearButton={true}
                debounceMs={500}
                containerStyle={styles.searchContainer}
            />

            {/* Map */}
            <View style={styles.mapContainer}>
                <MapView
                    ref={mapRef}
                    style={styles.map}
                    region={mapRegion}
                    onRegionChangeComplete={handleRegionChange}
                    showsUserLocation={true}
                    showsMyLocationButton={false}
                    showsCompass={true}
                    showsScale={true}
                >
                    {/* Current location marker */}
                    {currentLocation && (
                        <Marker
                            coordinate={currentLocation}
                            title="Your Location"
                            pinColor="#4CAF50"
                        />
                    )}

                    {/* Search radius circle */}
                    {showRadius && currentLocation && (
                        <Circle
                            center={currentLocation}
                            radius={radius * 1000} // Convert km to meters
                            strokeColor={theme.PRIMARY}
                            strokeWidth={2}
                            fillColor={`${theme.PRIMARY}20`}
                        />
                    )}

                    {/* Dynamic markers */}
                    {renderMarkers()}

                    {/* Search result markers */}
                    {searchResults.map((result, index) => (
                        <Marker
                            key={`search-${index}`}
                            coordinate={{
                                latitude: result.latitude,
                                longitude: result.longitude,
                            }}
                            pinColor="#FF5722"
                            title={
                                result.name ||
                                result.formattedAddress ||
                                'Search Result'
                            }
                        />
                    ))}
                </MapView>

                {/* My Location Button */}
                <TouchableOpacity
                    style={[
                        styles.myLocationButton,
                        { backgroundColor: theme.BACKGROUND },
                    ]}
                    onPress={handleMyLocationPress}
                >
                    {currentLocation ? (
                        <Ionicons
                            name="locate"
                            size={24}
                            color={theme.PRIMARY}
                        />
                    ) : (
                        <Ionicons
                            name="locate-outline"
                            size={24}
                            color={theme.TEXT_PLACEHOLDER}
                        />
                    )}
                </TouchableOpacity>
            </View>

            {/* Layer Selection */}
            {renderLayerButtons()}

            {/* Filter Modal */}
            {renderFilterModal()}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        paddingTop: 10,
        paddingBottom: 5,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        margin: 10,
        paddingHorizontal: 10,
        paddingVertical: 2,
        borderRadius: 8,
        elevation: 1,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    searchInput: {
        flex: 1,
        marginLeft: 10,
        fontSize: 14,
    },
    mapContainer: {
        flex: 1,
        marginHorizontal: 10,
        position: 'relative',
    },
    map: {
        flex: 1,
    },
    myLocationButton: {
        position: 'absolute',
        bottom: 10,
        right: 10,
        width: 50,
        height: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center',
    },
    layerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingVertical: 12,
        paddingHorizontal: 16,
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    layerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        flexShrink: 0, // Prevent shrinking
        minWidth: 'auto', // Allow natural width
        marginRight: 8,
    },
    layerButtonText: {
        marginLeft: 4,
        fontSize: 12,
        fontWeight: '600',
    },
    calloutContainer: {
        minWidth: 150,
        padding: 8,
    },
    calloutTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        marginBottom: 2,
    },
    calloutPrice: {
        fontSize: 12,
        color: '#4CAF50',
        fontWeight: '600',
    },
    calloutSubtitle: {
        fontSize: 12,
        color: '#666',
        marginBottom: 2,
    },
    calloutDistance: {
        fontSize: 10,
        color: '#999',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'flex-end',
    },
    modalContent: {
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        maxHeight: '70%',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    modalTitle: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    modalBody: {
        padding: 20,
    },
    filterSection: {
        marginBottom: 10,
    },
    filterLabel: {
        fontSize: 12,
        fontWeight: '600',
        marginBottom: 12,
    },
    radiusContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    radiusButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        flexShrink: 0, // Prevent shrinking
        minWidth: 'auto', // Allow natural width
    },
    radiusButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },
    toggleButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default MapExplorer;
