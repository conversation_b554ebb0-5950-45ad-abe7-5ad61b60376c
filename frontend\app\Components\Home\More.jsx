import React, { useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../../context/ThemeContext';
import { AuthContext } from '../../../context/AuthContext';

const More = () => {
    const { theme } = useContext(ThemeContext);
    const { user, logout } = useContext(AuthContext);
    const router = useRouter();

    const menuItems = [
        {
            id: 'contractors',
            title: 'Find Contractors',
            subtitle: 'Browse and hire contractors',
            icon: 'hammer',
            color: '#FF9800',
            onPress: () => router.push('/Contractors/ContractorList'),
        },
        {
            id: 'map',
            title: 'Map Explorer',
            subtitle: 'Explore properties on map',
            icon: 'map',
            color: '#4CAF50',
            onPress: () => router.push('/Map/MapExplorer'),
        },
        {
            id: 'ai',
            title: 'AI Assistant',
            subtitle: 'Get AI-powered recommendations',
            icon: 'sparkles',
            color: '#9C27B0',
            onPress: () => router.push('/AI/AIAssistant'),
        },
        {
            id: 'payment',
            title: 'Payment & Wallet',
            subtitle: 'Manage payments and wallet',
            icon: 'wallet',
            color: '#2196F3',
            onPress: () => router.push('/Payment/WalletScreen'),
        },
        {
            id: 'notifications',
            title: 'Notifications',
            subtitle: 'View all notifications',
            icon: 'notifications',
            color: '#FF5722',
            onPress: () => router.push('/Notifications/NotificationsList'),
        },
        {
            id: 'favorites',
            title: 'Saved Properties',
            subtitle: 'Your favorite properties',
            icon: 'heart',
            color: '#E91E63',
            onPress: () => router.push('/Properties/SavedProperties'),
        },
        {
            id: 'documents',
            title: 'My Documents',
            subtitle: 'Manage your documents',
            icon: 'document-text',
            color: '#607D8B',
            onPress: () => router.push('/Documents/DocumentManager'),
        },
        {
            id: 'analytics',
            title: 'Market Analytics',
            subtitle: 'View market insights',
            icon: 'analytics',
            color: '#795548',
            onPress: () => router.push('/Analytics/MarketAnalytics'),
        },
    ];

    // Add admin-specific items
    if (user?.role === 'admin') {
        menuItems.push({
            id: 'admin',
            title: 'Admin Dashboard',
            subtitle: 'Manage platform',
            icon: 'shield-checkmark',
            color: '#F44336',
            onPress: () => router.push('/Admin/AdminDashboard'),
        });
    }

    // Add broker-specific items
    if (user?.role === 'broker') {
        menuItems.push({
            id: 'broker-dashboard',
            title: 'Broker Dashboard',
            subtitle: 'Manage your listings',
            icon: 'business',
            color: '#3F51B5',
            onPress: () => router.push('/Broker/BrokerDashboard'),
        });
    }

    // Add contractor-specific items
    if (user?.role === 'contractor') {
        menuItems.push({
            id: 'contractor-dashboard',
            title: 'Contractor Dashboard',
            subtitle: 'Manage your services',
            icon: 'construct',
            color: '#FF6F00',
            onPress: () => router.push('/Contractors/ContractorDashboard'),
        });
    }

    const settingsItems = [
        {
            id: 'settings',
            title: 'Settings',
            icon: 'settings',
            onPress: () => router.push('/Settings/Settings'),
        },
        {
            id: 'help',
            title: 'Help & Support',
            icon: 'help-circle',
            onPress: () => router.push('/Support/HelpSupport'),
        },
        {
            id: 'about',
            title: 'About',
            icon: 'information-circle',
            onPress: () => router.push('/About/About'),
        },
        {
            id: 'logout',
            title: 'Logout',
            icon: 'log-out',
            color: '#F44336',
            onPress: () => {
                Alert.alert('Logout', 'Are you sure you want to logout?', [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Logout',
                        style: 'destructive',
                        onPress: () => {
                            logout();
                            router.replace('/auth/Login');
                        },
                    },
                ]);
            },
        },
    ];

    const renderMenuItem = (item) => (
        <TouchableOpacity
            key={item.id}
            style={[
                styles.menuItem,
                { backgroundColor: theme.CARD_BACKGROUND },
            ]}
            onPress={item.onPress}
            activeOpacity={0.7}
        >
            <View
                style={[
                    styles.menuIcon,
                    { backgroundColor: `${item.color || theme.PRIMARY}20` },
                ]}
            >
                <Ionicons
                    name={item.icon}
                    size={24}
                    color={item.color || theme.PRIMARY}
                />
            </View>
            <View style={styles.menuContent}>
                <Text style={[styles.menuTitle, { color: theme.TEXT_PRIMARY }]}>
                    {item.title}
                </Text>
                {item.subtitle && (
                    <Text
                        style={[
                            styles.menuSubtitle,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {item.subtitle}
                    </Text>
                )}
            </View>
            <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.TEXT_SECONDARY}
            />
        </TouchableOpacity>
    );

    const renderSettingsItem = (item) => (
        <TouchableOpacity
            key={item.id}
            style={[styles.settingsItem, { borderBottomColor: theme.BORDER }]}
            onPress={item.onPress}
            activeOpacity={0.7}
        >
            <View style={styles.settingsContent}>
                <Ionicons
                    name={item.icon}
                    size={20}
                    color={item.color || theme.TEXT_SECONDARY}
                />
                <Text
                    style={[
                        styles.settingsTitle,
                        { color: item.color || theme.TEXT_PRIMARY },
                    ]}
                >
                    {item.title}
                </Text>
            </View>
            <Ionicons
                name="chevron-forward"
                size={16}
                color={theme.TEXT_SECONDARY}
            />
        </TouchableOpacity>
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <View style={styles.userInfo}>
                        <View
                            style={[styles.avatar, { backgroundColor: '#fff' }]}
                        >
                            <Text
                                style={[
                                    styles.avatarText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                            </Text>
                        </View>
                        <View style={styles.userDetails}>
                            <Text style={styles.userName}>
                                {user?.name || 'User'}
                            </Text>
                            <Text style={styles.userRole}>
                                {user?.role?.charAt(0)?.toUpperCase() +
                                    user?.role?.slice(1) || 'Member'}
                            </Text>
                        </View>
                    </View>
                    <TouchableOpacity
                        onPress={() => navigation.navigate('Profile')}
                        style={styles.profileButton}
                    >
                        <Ionicons name="person-circle" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>
            </LinearGradient>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.scrollContent}
            >
                {/* Main Features */}
                <View style={styles.section}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Features
                    </Text>
                    <View style={styles.menuGrid}>
                        {menuItems.map(renderMenuItem)}
                    </View>
                </View>

                {/* Settings */}
                <View style={[styles.section, styles.settingsSection]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Settings & Support
                    </Text>
                    <View
                        style={[
                            styles.settingsContainer,
                            { backgroundColor: theme.CARD_BACKGROUND },
                        ]}
                    >
                        {settingsItems.map(renderSettingsItem)}
                    </View>
                </View>

                {/* App Info */}
                <View style={styles.appInfo}>
                    <Text
                        style={[
                            styles.appVersion,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        BuildConnect v1.0.0
                    </Text>
                    <Text
                        style={[
                            styles.appCopyright,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        © 2024 BuildConnect. All rights reserved.
                    </Text>
                </View>

                <View style={{ height: 100 }} />
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 50,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    userInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    avatar: {
        width: 50,
        height: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    avatarText: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    userDetails: {
        flex: 1,
    },
    userName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#fff',
    },
    userRole: {
        fontSize: 14,
        color: '#fff',
        opacity: 0.8,
    },
    profileButton: {
        padding: 8,
    },
    content: {
        flex: 1,
    },
    scrollContent: {
        paddingBottom: 20,
    },
    section: {
        marginTop: 20,
        paddingHorizontal: 20,
    },
    settingsSection: {
        marginTop: 30,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    menuGrid: {
        gap: 12,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    menuIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
    },
    menuContent: {
        flex: 1,
    },
    menuTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 2,
    },
    menuSubtitle: {
        fontSize: 12,
    },
    settingsContainer: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    settingsItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderBottomWidth: 1,
    },
    settingsContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    settingsTitle: {
        fontSize: 16,
        fontWeight: '500',
        marginLeft: 12,
    },
    appInfo: {
        alignItems: 'center',
        marginTop: 30,
        paddingHorizontal: 20,
    },
    appVersion: {
        fontSize: 14,
        marginBottom: 4,
    },
    appCopyright: {
        fontSize: 12,
        textAlign: 'center',
    },
});

export default More;
