import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    SafeAreaView,
    TextInput,
    Alert,
    Linking,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import SearchBar from '../Components/Shared/SearchBar';

const HelpSupport = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [selectedTab, setSelectedTab] = useState('faq');
    const [searchQuery, setSearchQuery] = useState('');
    const [contactForm, setContactForm] = useState({
        subject: '',
        message: '',
        category: 'general',
    });

    const handleSearch = (query) => {
        setSearchQuery(query);
    };

    const handleSearchSubmit = (query) => {
        setSearchQuery(query);
    };

    const faqData = [
        {
            id: 1,
            question: 'How do I create a property listing?',
            answer: 'To create a property listing, go to the "Add" tab and select "List Property". Fill in all the required details including photos, description, and pricing. Your listing will be reviewed and published within 24 hours.',
            category: 'listings',
        },
        {
            id: 2,
            question: 'How do I find contractors?',
            answer: 'Navigate to the "More" tab and select "Find Contractors". You can filter by location, specialty, rating, and budget. View their profiles, reviews, and contact them directly through the app.',
            category: 'contractors',
        },
        {
            id: 3,
            question: 'How does the payment system work?',
            answer: 'BuildConnect uses secure payment gateways. You can add money to your wallet, make payments to contractors, and receive payments from clients. All transactions are encrypted and secure.',
            category: 'payments',
        },
        {
            id: 4,
            question: 'How do I verify my profile?',
            answer: 'Go to Settings > Security & Privacy > Profile Verification. Upload required documents like ID proof, address proof, and professional certificates. Verification typically takes 2-3 business days.',
            category: 'account',
        },
        {
            id: 5,
            question: 'Can I edit my listing after publishing?',
            answer: 'Yes, you can edit your listings anytime. Go to your profile, select the listing you want to edit, and make the necessary changes. Updated listings are re-reviewed for quality.',
            category: 'listings',
        },
    ];

    const contactOptions = [
        {
            id: 'email',
            title: 'Email Support',
            subtitle: '<EMAIL>',
            icon: 'mail-outline',
            color: '#4CAF50',
            onPress: () => Linking.openURL('mailto:<EMAIL>'),
        },
        {
            id: 'phone',
            title: 'Phone Support',
            subtitle: '+91 1800-123-4567',
            icon: 'call-outline',
            color: '#2196F3',
            onPress: () => Linking.openURL('tel:+************'),
        },
        {
            id: 'chat',
            title: 'Live Chat',
            subtitle: 'Available 9 AM - 6 PM',
            icon: 'chatbubble-outline',
            color: '#FF9800',
            onPress: () => router.push('/Support/LiveChat'),
        },
        {
            id: 'whatsapp',
            title: 'WhatsApp',
            subtitle: '+91 98765-43210',
            icon: 'logo-whatsapp',
            color: '#25D366',
            onPress: () => Linking.openURL('https://wa.me/************'),
        },
    ];

    const categories = [
        { id: 'general', name: 'General' },
        { id: 'listings', name: 'Listings' },
        { id: 'contractors', name: 'Contractors' },
        { id: 'payments', name: 'Payments' },
        { id: 'account', name: 'Account' },
        { id: 'technical', name: 'Technical' },
    ];

    const filteredFAQs = faqData.filter(
        (faq) =>
            faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
            faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const handleSubmitTicket = () => {
        if (!contactForm.subject.trim() || !contactForm.message.trim()) {
            Alert.alert('Error', 'Please fill in all required fields.');
            return;
        }

        Alert.alert(
            'Ticket Submitted',
            'Your support ticket has been submitted successfully. We will get back to you within 24 hours.',
            [
                {
                    text: 'OK',
                    onPress: () => {
                        setContactForm({
                            subject: '',
                            message: '',
                            category: 'general',
                        });
                    },
                },
            ]
        );
    };

    const renderFAQ = (faq) => (
        <TouchableOpacity
            key={faq.id}
            style={[
                styles.faqCard,
                { backgroundColor: theme.CARD, shadowColor: theme.PRIMARY },
            ]}
            onPress={() => router.push(`/Support/FAQDetails?id=${faq.id}`)}
        >
            <View style={styles.faqHeader}>
                <Text
                    style={[styles.faqQuestion, { color: theme.TEXT_PRIMARY }]}
                >
                    {faq.question}
                </Text>
                <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={theme.TEXT_SECONDARY}
                />
            </View>
            <Text
                style={[styles.faqAnswer, { color: theme.TEXT_SECONDARY }]}
                numberOfLines={2}
            >
                {faq.answer}
            </Text>
        </TouchableOpacity>
    );

    const renderContactOption = (option) => (
        <TouchableOpacity
            key={option.id}
            style={[
                styles.contactCard,
                { backgroundColor: theme.CARD, shadowColor: theme.PRIMARY },
            ]}
            onPress={option.onPress}
        >
            <View
                style={[
                    styles.contactIcon,
                    { backgroundColor: option.color + '20' },
                ]}
            >
                <Ionicons name={option.icon} size={24} color={option.color} />
            </View>
            <View style={styles.contactInfo}>
                <Text
                    style={[styles.contactTitle, { color: theme.TEXT_PRIMARY }]}
                >
                    {option.title}
                </Text>
                <Text
                    style={[
                        styles.contactSubtitle,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {option.subtitle}
                </Text>
            </View>
            <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.TEXT_SECONDARY}
            />
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons
                            name="arrow-back"
                            size={24}
                            color={theme.WHITE}
                        />
                    </TouchableOpacity>
                    <Text style={[styles.headerTitle, { color: theme.WHITE }]}>
                        Help & Support
                    </Text>
                    <View style={{ width: 24 }} />
                </View>
            </LinearGradient>

            {/* Tabs */}
            <View style={[styles.tabsContainer]}>
                <TouchableOpacity
                    style={[
                        styles.tab,
                        selectedTab === 'faq'
                            ? {
                                  backgroundColor: theme.PRIMARY,
                                  borderColor: theme.PRIMARY,
                              }
                            : {
                                  borderColor: theme.GRAY_LIGHT,
                              },
                    ]}
                    onPress={() => setSelectedTab('faq')}
                >
                    <Text
                        style={[
                            styles.tabText,
                            {
                                color:
                                    selectedTab === 'faq'
                                        ? '#fff'
                                        : theme.TEXT_SECONDARY,
                            },
                        ]}
                    >
                        FAQ
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.tab,
                        selectedTab === 'contact'
                            ? {
                                  backgroundColor: theme.PRIMARY,
                                  borderColor: theme.PRIMARY,
                              }
                            : {
                                  borderColor: theme.GRAY_LIGHT,
                              },
                    ]}
                    onPress={() => setSelectedTab('contact')}
                >
                    <Text
                        style={[
                            styles.tabText,
                            {
                                color:
                                    selectedTab === 'contact'
                                        ? '#fff'
                                        : theme.TEXT_SECONDARY,
                            },
                        ]}
                    >
                        Contact Us
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.tab,
                        selectedTab === 'ticket'
                            ? {
                                  backgroundColor: theme.PRIMARY,
                                  borderColor: theme.PRIMARY,
                              }
                            : {
                                  borderColor: theme.GRAY_LIGHT,
                              },
                    ]}
                    onPress={() => setSelectedTab('ticket')}
                >
                    <Text
                        style={[
                            styles.tabText,
                            {
                                color:
                                    selectedTab === 'ticket'
                                        ? theme.WHITE
                                        : theme.TEXT_SECONDARY,
                            },
                        ]}
                    >
                        Submit Ticket
                    </Text>
                </TouchableOpacity>
            </View>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
            >
                {selectedTab === 'faq' && (
                    <View style={styles.faqSection}>
                        {/* Search */}
                        <SearchBar
                            placeholder="Search FAQs..."
                            onSearch={handleSearch}
                            onSubmit={handleSearchSubmit}
                            showClearButton={true}
                            debounceMs={300}
                            containerStyle={styles.searchContainer}
                        />

                        {/* FAQ List */}
                        <View style={styles.faqList}>
                            {filteredFAQs.map(renderFAQ)}
                        </View>
                    </View>
                )}

                {selectedTab === 'contact' && (
                    <View style={styles.contactSection}>
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Get in touch with us
                        </Text>
                        <Text
                            style={[
                                styles.sectionSubtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Choose your preferred way to contact our support
                            team
                        </Text>

                        <View style={styles.contactList}>
                            {contactOptions.map(renderContactOption)}
                        </View>
                    </View>
                )}

                {selectedTab === 'ticket' && (
                    <View style={styles.ticketSection}>
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Submit a Support Ticket
                        </Text>
                        <Text
                            style={[
                                styles.sectionSubtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Describe your issue and we'll get back to you soon
                        </Text>

                        <View style={styles.form}>
                            {/* Category */}
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Category
                            </Text>
                            <ScrollView
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                style={styles.categoriesContainer}
                            >
                                {categories.map((category) => (
                                    <TouchableOpacity
                                        key={category.id}
                                        style={[
                                            styles.categoryButton,
                                            {
                                                backgroundColor:
                                                    contactForm.category ===
                                                    category.id
                                                        ? theme.PRIMARY
                                                        : theme.CARD,
                                                borderColor: theme.GRAY_LIGHT,
                                            },
                                        ]}
                                        onPress={() =>
                                            setContactForm((prev) => ({
                                                ...prev,
                                                category: category.id,
                                            }))
                                        }
                                    >
                                        <Text
                                            style={[
                                                styles.categoryText,
                                                {
                                                    color:
                                                        contactForm.category ===
                                                        category.id
                                                            ? '#fff'
                                                            : theme.TEXT_SECONDARY,
                                                },
                                            ]}
                                        >
                                            {category.name}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </ScrollView>

                            {/* Subject */}
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Subject *
                            </Text>
                            <TextInput
                                style={[
                                    styles.input,
                                    {
                                        backgroundColor: theme.CARD,
                                        color: theme.TEXT_PRIMARY,
                                    },
                                ]}
                                placeholder="Brief description of your issue"
                                placeholderTextColor={theme.TEXT_SECONDARY}
                                value={contactForm.subject}
                                onChangeText={(text) =>
                                    setContactForm((prev) => ({
                                        ...prev,
                                        subject: text,
                                    }))
                                }
                            />

                            {/* Message */}
                            <Text
                                style={[
                                    styles.label,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Message *
                            </Text>
                            <TextInput
                                style={[
                                    styles.textArea,
                                    {
                                        backgroundColor: theme.CARD,
                                        color: theme.TEXT_PRIMARY,
                                    },
                                ]}
                                placeholder="Provide detailed information about your issue..."
                                placeholderTextColor={theme.TEXT_SECONDARY}
                                value={contactForm.message}
                                onChangeText={(text) =>
                                    setContactForm((prev) => ({
                                        ...prev,
                                        message: text,
                                    }))
                                }
                                multiline
                                numberOfLines={6}
                                textAlignVertical="top"
                            />

                            {/* Submit Button */}
                            <TouchableOpacity
                                style={[
                                    styles.submitButton,
                                    { backgroundColor: theme.PRIMARY },
                                ]}
                                onPress={handleSubmitTicket}
                            >
                                <Text style={styles.submitButtonText}>
                                    Submit Ticket
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 10,
        paddingBottom: 10,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    tabsContainer: {
        flexDirection: 'row',
        margin: 10,
        borderRadius: 6,
        padding: 2,
    },
    tab: {
        flex: 1,
        paddingVertical: 8,
        marginRight: 4,
        borderRadius: 6,
        borderWidth: 1,
        alignItems: 'center',
    },
    tabText: {
        fontSize: 12,
        fontWeight: '600',
    },
    content: {
        flex: 1,
        paddingHorizontal: 20,
    },
    contactSection: {
        marginBottom: 10,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    sectionSubtitle: {
        fontSize: 14,
        marginBottom: 24,
        lineHeight: 22,
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 12,
        marginBottom: 20,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 6,
    },
    searchInput: {
        flex: 1,
        marginLeft: 10,
        fontSize: 12,
    },
    faqList: {
        marginBottom: 10,
    },
    faqCard: {
        padding: 16,
        borderRadius: 12,
        marginBottom: 6,
        elevation: 4,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    faqHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    faqQuestion: {
        fontSize: 14,
        fontWeight: '600',
        flex: 1,
    },
    faqAnswer: {
        fontSize: 12,
        lineHeight: 20,
    },
    contactList: {
        marginBottom: 12,
    },
    contactCard: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderRadius: 12,
        marginBottom: 6,
        elevation: 4,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    contactIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    contactInfo: {
        flex: 1,
    },
    contactTitle: {
        fontSize: 14,
        fontWeight: '600',
        marginBottom: 4,
    },
    contactSubtitle: {
        fontSize: 12,
    },
    form: {
        marginBottom: 40,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 10,
        marginTop: 2,
    },
    categoriesContainer: {
        marginBottom: 2,
    },
    categoryButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderWidth: 1,
        borderRadius: 20,
        marginRight: 10,
        flexShrink: 0, // Prevent shrinking
        minWidth: 'auto', // Allow natural width
    },
    categoryText: {
        fontSize: 12,
        fontWeight: '600',
    },
    input: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 12,
        fontSize: 14,
        marginBottom: 8,
    },
    textArea: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 12,
        fontSize: 14,
        height: 120,
        marginBottom: 24,
    },
    submitButton: {
        paddingVertical: 16,
        borderRadius: 12,
        alignItems: 'center',
    },
    submitButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#fff',
    },
});

export default HelpSupport;
