import React, { useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Image,
    Animated,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ThemeContext } from '../../../context/ThemeContext';

const BannerSlider = () => {
    const { theme } = React.useContext(ThemeContext);
    const router = useRouter();

    const images = [
        require('../../../assets/images/banner(1).png'),
        require('../../../assets/images/banner(2).jpg'),
        require('../../../assets/images/banner(3).jpg'),
        require('../../../assets/images/banner(4).png'),
    ];

    const bannerTitle = [
        'Build Your Future',
        'Connect with Trusted Contractors',
        'Connect with Trusted Site Scouts',
        'Explore Dream Land',
    ];

    const bannerSubtitle = [
        'Your Land & Construction Hub',
        'Find Verified Professionals',
        'Find Verified Site Scouts',
        'Find Your Dream Land',
    ];

    const bannerButton = [
        'Start Exploring',
        'Find Contractors',
        'Find Site Scouts',
        'Explore Properties',
    ];

    const routes = [
        '/Properties/LandList',
        '/Contractors/ContractorList',
        '/Broker/BrokerList',
        '/Properties/LandList',
    ];

    const [currentIndex, setCurrentIndex] = useState(0);
    const fadeAnim = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        const interval = setInterval(() => {
            // Fade out
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 500,
                useNativeDriver: true,
            }).start(() => {
                // Change image after fade out
                setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);

                // Fade in
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 500,
                    useNativeDriver: true,
                }).start();
            });
        }, 5000); // Change image every 5 seconds

        return () => clearInterval(interval);
    }, [fadeAnim]);

    return (
        <Animated.View
            style={[
                styles.bannerCard,
                { backgroundColor: theme.CARD, opacity: fadeAnim },
            ]}
        >
            <Image
                source={images[currentIndex]}
                style={styles.bannerCardImage}
                resizeMode="cover"
            />
            <LinearGradient
                colors={[theme.BLACK + '80', theme.BLACK + '40']}
                style={styles.bannerCardOverlay}
            >
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'flex-end',
                        marginTop: 60,
                    }}
                >
                    <Text style={[styles.bannerTitle, { color: theme.WHITE }]}>
                        {bannerTitle[currentIndex]}
                    </Text>
                    <Text
                        style={[styles.bannerSubtitle, { color: theme.WHITE }]}
                    >
                        {bannerSubtitle[currentIndex]}
                    </Text>
                </View>
                <TouchableOpacity
                    style={styles.bannerButton}
                    onPress={() => router.push(routes[currentIndex])}
                    activeOpacity={0.85}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                        style={styles.bannerButtonGradient}
                    >
                        <Text
                            style={[
                                styles.bannerButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            {bannerButton[currentIndex]}
                        </Text>
                        <Ionicons
                            name="chevron-forward"
                            size={18}
                            color={theme.WHITE}
                            style={{ marginLeft: 6 }}
                        />
                    </LinearGradient>
                </TouchableOpacity>
            </LinearGradient>
        </Animated.View>
    );
};

export default BannerSlider;
const styles = StyleSheet.create({
    bannerCard: {
        height: 180,
        marginHorizontal: 8,
        marginTop: 6,
        borderRadius: 12,
        overflow: 'hidden',
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
        elevation: 5,
    },
    bannerCardImage: {
        width: '100%',
        height: '100%',
        position: 'absolute',
    },
    bannerCardOverlay: {
        flex: 1,
        padding: 10,
        justifyContent: 'space-between',
    },
    bannerTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 2,
        marginTop: 20,
        textShadowColor: 'rgba(0,0,0,0.4)',
        textShadowOffset: { width: 0, height: 2 },
        textShadowRadius: 6,
    },
    bannerSubtitle: {
        fontSize: 10,
        fontStyle: 'italic',
        marginBottom: 16,
        opacity: 0.95,
    },
    bannerButton: {
        alignSelf: 'flex-end',
    },
    bannerButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    bannerButtonText: {
        fontSize: 12,
        fontWeight: '600',
    },
});
