import { View, StyleSheet } from 'react-native';
import React, { useContext, useState, useCallback } from 'react';
import SearchBar from '../Components/Shared/SearchBar';
import Header from '../Components/Home/Header';
import Users from '../Components/Chats/Users';
import { ThemeContext } from '../../context/ThemeContext';

export default function Chats() {
    const { theme } = useContext(ThemeContext);
    const [searchQuery, setSearchQuery] = useState('');
    const [isSearching, setIsSearching] = useState(false);

    const handleSearch = useCallback(async (query) => {
        if (!query.trim()) {
            setSearchQuery('');
            return;
        }

        setIsSearching(true);
        try {
            // Simulate search delay
            await new Promise((resolve) => setTimeout(resolve, 500));
            setSearchQuery(query);
        } catch (error) {
            console.error('Search failed:', error);
        } finally {
            setIsSearching(false);
        }
    }, []);

    const handleSearchSubmit = useCallback(
        (query) => {
            console.log('Search submitted:', query);
            handleSearch(query);
        },
        [handleSearch]
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <View style={styles.fixedTop}>
                <Header />
                <SearchBar
                    placeholder="Search chats..."
                    onSearch={handleSearch}
                    onSubmit={handleSearchSubmit}
                    isLoading={isSearching}
                    showClearButton={true}
                    debounceMs={300}
                />
            </View>
            <View style={styles.scrollableList}>
                <Users searchQuery={searchQuery} />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    fixedTop: {
        paddingBottom: 8,
    },
    scrollableList: {
        flex: 1,
    },
});
