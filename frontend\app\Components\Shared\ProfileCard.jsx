import React, { useContext } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    Animated,
    Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';

const { width } = Dimensions.get('window');
const cardWidth = (width - 48) / 3;

export default function ProfileCard({
    id,
    name,
    serviceAreas,
    ratings,
    image,
    onPress,
    accessibilityLabel,
}) {
    const { theme } = useContext(ThemeContext);
    const scaleAnim = React.useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
        Animated.spring(scaleAnim, {
            toValue: 0.95,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <Ionicons key={i} name="star" size={12} color="#FFD700" />
            );
        }

        if (hasHalfStar) {
            stars.push(
                <Ionicons
                    key="half"
                    name="star-half"
                    size={12}
                    color="#FFD700"
                />
            );
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(
                <Ionicons
                    key={`empty-${i}`}
                    name="star-outline"
                    size={12}
                    color="#FFD700"
                />
            );
        }

        return stars;
    };

    return (
        <TouchableOpacity
            style={[
                styles.container,
                { backgroundColor: theme.CARD, shadowColor: theme.PRIMARY },
            ]}
            onPress={onPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.9}
            accessibilityLabel={accessibilityLabel}
        >
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                {/* Avatar Section */}
                <View style={styles.avatarContainer}>
                    {image ? (
                        <Image
                            source={{ uri: image }}
                            style={[
                                styles.avatar,
                                { borderColor: theme.PRIMARY },
                            ]}
                            resizeMode="cover"
                        />
                    ) : (
                        <View
                            style={[
                                styles.avatarPlaceholder,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.PRIMARY,
                                },
                            ]}
                        >
                            <Ionicons
                                name="person"
                                size={32}
                                color={theme.TEXT_SECONDARY}
                            />
                        </View>
                    )}
                </View>

                {/* Name */}
                <Text
                    style={[styles.name, { color: theme.TEXT_PRIMARY }]}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                >
                    {name}
                </Text>

                {/* Service Area */}
                <Text
                    style={[
                        styles.serviceArea,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                >
                    {serviceAreas && serviceAreas.length > 0
                        ? serviceAreas[0]
                        : 'Service Area'}
                </Text>

                {/* Rating */}
                <View style={styles.ratingContainer}>
                    <View style={styles.starsContainer}>
                        {renderStars(ratings || 0)}
                    </View>
                    <Text
                        style={[
                            styles.ratingText,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {(ratings || 0).toFixed(1)}
                    </Text>
                </View>
            </Animated.View>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    container: {
        width: cardWidth,
        height: cardWidth * 1.4, // Square card
        borderRadius: 12,
        padding: 10,
        marginHorizontal: 4,
        marginBottom: 8,
        alignItems: 'center',
        justifyContent: 'flex-start',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    avatarContainer: {
        alignItems: 'center',
        marginBottom: 6,
    },
    avatar: {
        width: 45,
        height: 45,
        borderRadius: 22.5,
        borderWidth: 1,
    },
    avatarPlaceholder: {
        width: 45,
        height: 45,
        borderRadius: 22.5,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    name: {
        fontSize: 13,
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 3,
        lineHeight: 16,
    },
    serviceArea: {
        fontSize: 11,
        textAlign: 'center',
        marginBottom: 6,
        lineHeight: 13,
    },
    ratingContainer: {
        alignItems: 'center',
        flex: 1,
        marginTop: 4,
        justifyContent: 'flex-end',
        paddingBottom: 2,
    },
    starsContainer: {
        flexDirection: 'row',
        marginBottom: 2,
    },
    ratingText: {
        fontSize: 10,
        fontWeight: '500',
    },
});
