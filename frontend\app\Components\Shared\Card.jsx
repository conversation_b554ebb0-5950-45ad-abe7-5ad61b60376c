import { View, Text, Image, TouchableOpacity, Animated } from 'react-native';
import React, { useContext } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';

// Card component for general use
export default function Card({
    title,
    name,
    location,
    price,
    image,
    verified,
    rating,
    projects,
    onPress,
    area,
    onInterestedPress,
    accessibilityLabel,
}) {
    const { theme } = useContext(ThemeContext);
    const scaleAnim = React.useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
        Animated.spring(scaleAnim, {
            toValue: 0.98,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    // Fallback for image: if image is a string (uri), use {uri: image}
    let imageSource = image;
    if (typeof image === 'string') {
        imageSource = { uri: image };
    }

    return (
        <TouchableOpacity
            style={{
                backgroundColor: theme.CARD,
                borderRadius: 4,
                overflow: 'hidden',
                shadowColor: theme.PRIMARY,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.12,
                shadowRadius: 8,
                elevation: 8,
                marginRight:3,
                marginBottom: 8,
                width: 150,
                minHeight: 120,
            }}
            onPress={onPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.9}
            accessibilityLabel={accessibilityLabel}
        >
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                {/* Image Section */}
                <View style={{ position: 'relative' }}>
                    <Image
                        source={imageSource}
                        style={{
                            width: '100%',
                            height: 100,
                        }}
                        resizeMode="cover"
                    />
                    <View
                        style={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            backgroundColor: theme.WHITE,
                            borderRadius: 8,
                            padding: 4,
                        }}
                    >
                        {verified && (
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginTop: 2,
                                }}
                            >
                                <Ionicons
                                    name="checkmark-circle-sharp"
                                    size={12}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={{
                                        color: theme.PRIMARY,
                                        fontSize: 8,
                                        marginLeft: 4,
                                    }}
                                >
                                    Verified
                                </Text>
                            </View>
                        )}
                    </View>
                    <View
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            backgroundColor: theme.BLACK + '80',
                            padding: 8,
                        }}
                    >
                        <Text
                            style={{
                                color: theme.WHITE,
                                fontSize: 12,
                                fontWeight: 'bold',
                            }}
                        >
                            {title || name}
                        </Text>
                    </View>
                </View>

                {/* Content Section */}
                <View style={{ padding: 4 }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            paddingHorizontal: 4,
                        }}
                    >
                        {location && (
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginTop: 2,
                                }}
                            >
                                <Ionicons
                                    name="location-outline"
                                    size={12}
                                    color={theme.GRAY}
                                />
                                <Text
                                    style={{
                                        color: theme.TEXT_SECONDARY,
                                        fontSize: 10,
                                        marginLeft: 4,
                                    }}
                                >
                                    {location}
                                </Text>
                            </View>
                        )}
                        {area && (
                            <Text
                                style={{
                                    color: theme.TEXT_SECONDARY,
                                    fontSize: 10,
                                    marginTop: 2,
                                }}
                            >
                                {area} sqft
                            </Text>
                        )}
                    </View>
                    <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingHorizontal: 6,
                    }}>
                        {price && (
                            <Text
                                style={{
                                    color: theme.PRIMARY,
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                    marginTop: 2,
                                    textAlign: 'center',
                                }}
                            >
                                ₹{price}
                            </Text>
                        )}
                        {rating && (
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginTop: 2,
                                }}
                            >
                                <Ionicons
                                    name="star"
                                    size={12}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={{
                                        color: theme.PRIMARY,
                                        fontSize: 12,
                                        fontWeight: 'bold',
                                        marginLeft: 4,
                                    }}
                                >
                                    {rating}
                                </Text>
                            </View>
                        )}
                    </View>
                    {projects && (
                        <Text
                            style={{
                                color: theme.TEXT_SECONDARY,
                                fontSize: 13,
                                marginTop: 2,
                            }}
                        >
                            {projects} Projects
                        </Text>
                    )}
                    {onInterestedPress && (
                        <TouchableOpacity
                            style={{
                                backgroundColor: theme.PRIMARY,
                                paddingHorizontal: 16,
                                paddingVertical: 8,
                                borderRadius: 8,
                                marginTop: 8,
                                alignItems: 'center',
                            }}
                            onPress={onInterestedPress}
                            accessibilityLabel={`Show interest in ${title || name}`}
                        >
                            <Text
                                style={{
                                    color: theme.WHITE,
                                    fontWeight: '600',
                                    fontSize: 12,
                                }}
                            >
                                Interested
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
            </Animated.View>
        </TouchableOpacity>
    );
}

// BrokerCard: for broker profile display
export function BrokerCard({
    name,
    nameOnAadhaar,
    aadhaarNumber,
    panNumber,
    panName,
    dateOfBirth,
    panDateOfBirth,
    gender,
    address,
    experience,
    serviceAreas,
    aadhaarAsset,
    panAsset,
    onPress,
    accessibilityLabel,
}) {
    const { theme } = useContext(ThemeContext);
    const scaleAnim = React.useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
        Animated.spring(scaleAnim, {
            toValue: 0.98,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const formatDate = (date) => {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString();
    };

    return (
        <TouchableOpacity
            style={{
                backgroundColor: theme.CARD,
                borderRadius: 16,
                overflow: 'hidden',
                shadowColor: theme.PRIMARY,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.12,
                shadowRadius: 8,
                elevation: 8,
                marginRight: 16,
                marginBottom: 8,
                width: 200,
                minHeight: 250,
            }}
            onPress={onPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.9}
            accessibilityLabel={accessibilityLabel}
        >
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                <View
                    style={{
                        padding: 14,
                        borderBottomWidth: 1,
                        borderBottomColor: theme.INPUT_BORDER,
                        backgroundColor: theme.PRIMARY + '10%',
                    }}
                >
                    <Text
                        style={{
                            color: theme.PRIMARY,
                            fontWeight: 'bold',
                            fontSize: 18,
                            marginBottom: 2,
                        }}
                    >
                        {name || panName || nameOnAadhaar || 'Broker'}
                    </Text>
                    <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13 }}>
                        Aadhaar: {aadhaarNumber}
                    </Text>
                    <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13 }}>
                        PAN: {panNumber}
                    </Text>
                </View>
                <View style={{ padding: 14 }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="person-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Name on Aadhaar: {nameOnAadhaar}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="calendar-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            DOB: {formatDate(dateOfBirth)}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="male-female-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Gender: {gender}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="home-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Address: {address}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="briefcase-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Experience: {experience} year
                            {experience > 1 ? 's' : ''}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                            flexWrap: 'wrap',
                        }}
                    >
                        <Ionicons
                            name="location-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                                flexShrink: 1,
                            }}
                        >
                            Service Areas:{' '}
                            {Array.isArray(serviceAreas)
                                ? serviceAreas.join(', ')
                                : ''}
                        </Text>
                    </View>
                </View>
            </Animated.View>
        </TouchableOpacity>
    );
}
